import {
  defaultTheme_default,
  identifier_default
} from "./chunk-J4FXFBWI.js";
import {
  useTheme_default
} from "./chunk-WD5ZKRWL.js";
import {
  require_react
} from "./chunk-R56R2YIZ.js";
import {
  __toESM
} from "./chunk-BYPFWIQ6.js";

// node_modules/@mui/material/styles/useTheme.js
var React = __toESM(require_react());
function useTheme() {
  const theme = useTheme_default(defaultTheme_default);
  if (true) {
    React.useDebugValue(theme);
  }
  return theme[identifier_default] || theme;
}

export {
  useTheme
};
//# sourceMappingURL=chunk-KVEADFL7.js.map
