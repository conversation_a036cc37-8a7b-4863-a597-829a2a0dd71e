{"version": 3, "sources": ["../../@mui/x-date-pickers/internals/components/PickerViewRoot/PickerViewRoot.js"], "sourcesContent": ["import { styled } from '@mui/material/styles';\nimport { DIALOG_WIDTH, VIEW_HEIGHT } from '../../constants/dimensions';\nexport const PickerViewRoot = styled('div')({\n  overflow: 'hidden',\n  width: DIALOG_WIDTH,\n  maxHeight: VIEW_HEIGHT,\n  display: 'flex',\n  flexDirection: 'column',\n  margin: '0 auto'\n});"], "mappings": ";;;;;;;;;AAEO,IAAM,iBAAiB,eAAO,KAAK,EAAE;AAAA,EAC1C,UAAU;AAAA,EACV,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,eAAe;AAAA,EACf,QAAQ;AACV,CAAC;", "names": []}