{"version": 3, "sources": ["../../@mui/x-date-pickers/hooks/useClearableField.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"ownerState\"];\nimport * as React from 'react';\nimport { useSlotProps } from '@mui/base/utils';\nimport MuiIconButton from '@mui/material/IconButton';\nimport InputAdornment from '@mui/material/InputAdornment';\nimport { ClearIcon } from '../icons';\nimport { useLocaleText } from '../internals';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const useClearableField = ({\n  clearable,\n  fieldProps: forwardedFieldProps,\n  InputProps: ForwardedInputProps,\n  onClear,\n  slots,\n  slotProps,\n  components,\n  componentsProps\n}) => {\n  var _ref, _slots$clearButton, _slotProps$clearButto, _ref2, _slots$clearIcon, _slotProps$clearIcon;\n  const localeText = useLocaleText();\n  const IconButton = (_ref = (_slots$clearButton = slots == null ? void 0 : slots.clearButton) != null ? _slots$clearButton : components == null ? void 0 : components.ClearButton) != null ? _ref : MuiIconButton;\n  // The spread is here to avoid this bug mui/material-ui#34056\n  const _useSlotProps = useSlotProps({\n      elementType: IconButton,\n      externalSlotProps: (_slotProps$clearButto = slotProps == null ? void 0 : slotProps.clearButton) != null ? _slotProps$clearButto : componentsProps == null ? void 0 : componentsProps.clearButton,\n      ownerState: {},\n      className: 'clearButton',\n      additionalProps: {\n        title: localeText.fieldClearLabel\n      }\n    }),\n    iconButtonProps = _objectWithoutPropertiesLoose(_useSlotProps, _excluded);\n  const EndClearIcon = (_ref2 = (_slots$clearIcon = slots == null ? void 0 : slots.clearIcon) != null ? _slots$clearIcon : components == null ? void 0 : components.ClearIcon) != null ? _ref2 : ClearIcon;\n  const endClearIconProps = useSlotProps({\n    elementType: EndClearIcon,\n    externalSlotProps: (_slotProps$clearIcon = slotProps == null ? void 0 : slotProps.clearIcon) != null ? _slotProps$clearIcon : componentsProps == null ? void 0 : componentsProps.clearIcon,\n    ownerState: {}\n  });\n  const InputProps = _extends({}, ForwardedInputProps, {\n    endAdornment: /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [clearable && /*#__PURE__*/_jsx(InputAdornment, {\n        position: \"end\",\n        sx: {\n          marginRight: ForwardedInputProps != null && ForwardedInputProps.endAdornment ? -1 : -1.5\n        },\n        children: /*#__PURE__*/_jsx(IconButton, _extends({}, iconButtonProps, {\n          onClick: onClear,\n          children: /*#__PURE__*/_jsx(EndClearIcon, _extends({\n            fontSize: \"small\"\n          }, endClearIconProps))\n        }))\n      }), ForwardedInputProps == null ? void 0 : ForwardedInputProps.endAdornment]\n    })\n  });\n  const fieldProps = _extends({}, forwardedFieldProps, {\n    sx: [{\n      '& .clearButton': {\n        opacity: 1\n      },\n      '@media (pointer: fine)': {\n        '& .clearButton': {\n          opacity: 0\n        },\n        '&:hover, &:focus-within': {\n          '.clearButton': {\n            opacity: 1\n          }\n        }\n      }\n    }, ...(Array.isArray(forwardedFieldProps.sx) ? forwardedFieldProps.sx : [forwardedFieldProps.sx])]\n  });\n  return {\n    InputProps,\n    fieldProps\n  };\n};"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAGA,YAAuB;AAMvB,yBAA4B;AAC5B,IAAAA,sBAA8B;AAR9B,IAAM,YAAY,CAAC,YAAY;AASxB,IAAM,oBAAoB,CAAC;AAAA,EAChC;AAAA,EACA,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,MAAI,MAAM,oBAAoB,uBAAuB,OAAO,kBAAkB;AAC9E,QAAM,aAAa,cAAc;AACjC,QAAM,cAAc,QAAQ,qBAAqB,SAAS,OAAO,SAAS,MAAM,gBAAgB,OAAO,qBAAqB,cAAc,OAAO,SAAS,WAAW,gBAAgB,OAAO,OAAO;AAEnM,QAAM,gBAAgB,aAAa;AAAA,IAC/B,aAAa;AAAA,IACb,oBAAoB,wBAAwB,aAAa,OAAO,SAAS,UAAU,gBAAgB,OAAO,wBAAwB,mBAAmB,OAAO,SAAS,gBAAgB;AAAA,IACrL,YAAY,CAAC;AAAA,IACb,WAAW;AAAA,IACX,iBAAiB;AAAA,MACf,OAAO,WAAW;AAAA,IACpB;AAAA,EACF,CAAC,GACD,kBAAkB,8BAA8B,eAAe,SAAS;AAC1E,QAAM,gBAAgB,SAAS,mBAAmB,SAAS,OAAO,SAAS,MAAM,cAAc,OAAO,mBAAmB,cAAc,OAAO,SAAS,WAAW,cAAc,OAAO,QAAQ;AAC/L,QAAM,oBAAoB,aAAa;AAAA,IACrC,aAAa;AAAA,IACb,oBAAoB,uBAAuB,aAAa,OAAO,SAAS,UAAU,cAAc,OAAO,uBAAuB,mBAAmB,OAAO,SAAS,gBAAgB;AAAA,IACjL,YAAY,CAAC;AAAA,EACf,CAAC;AACD,QAAM,aAAa,SAAS,CAAC,GAAG,qBAAqB;AAAA,IACnD,kBAA2B,oBAAAC,MAAY,gBAAU;AAAA,MAC/C,UAAU,CAAC,iBAA0B,mBAAAC,KAAK,wBAAgB;AAAA,QACxD,UAAU;AAAA,QACV,IAAI;AAAA,UACF,aAAa,uBAAuB,QAAQ,oBAAoB,eAAe,KAAK;AAAA,QACtF;AAAA,QACA,cAAuB,mBAAAA,KAAK,YAAY,SAAS,CAAC,GAAG,iBAAiB;AAAA,UACpE,SAAS;AAAA,UACT,cAAuB,mBAAAA,KAAK,cAAc,SAAS;AAAA,YACjD,UAAU;AAAA,UACZ,GAAG,iBAAiB,CAAC;AAAA,QACvB,CAAC,CAAC;AAAA,MACJ,CAAC,GAAG,uBAAuB,OAAO,SAAS,oBAAoB,YAAY;AAAA,IAC7E,CAAC;AAAA,EACH,CAAC;AACD,QAAM,aAAa,SAAS,CAAC,GAAG,qBAAqB;AAAA,IACnD,IAAI,CAAC;AAAA,MACH,kBAAkB;AAAA,QAChB,SAAS;AAAA,MACX;AAAA,MACA,0BAA0B;AAAA,QACxB,kBAAkB;AAAA,UAChB,SAAS;AAAA,QACX;AAAA,QACA,2BAA2B;AAAA,UACzB,gBAAgB;AAAA,YACd,SAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF;AAAA,IACF,GAAG,GAAI,MAAM,QAAQ,oBAAoB,EAAE,IAAI,oBAAoB,KAAK,CAAC,oBAAoB,EAAE,CAAE;AAAA,EACnG,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "_jsxs", "_jsx"]}