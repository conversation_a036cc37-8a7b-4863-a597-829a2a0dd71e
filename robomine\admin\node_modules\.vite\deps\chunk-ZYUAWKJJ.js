import {
  defaultTheme_default,
  identifier_default
} from "./chunk-J4FXFBWI.js";
import {
  useThemeProps
} from "./chunk-WD5ZKRWL.js";

// node_modules/@mui/material/styles/useThemeProps.js
function useThemeProps2({
  props,
  name
}) {
  return useThemeProps({
    props,
    name,
    defaultTheme: defaultTheme_default,
    themeId: identifier_default
  });
}

export {
  useThemeProps2 as useThemeProps
};
//# sourceMappingURL=chunk-ZYUAWKJJ.js.map
