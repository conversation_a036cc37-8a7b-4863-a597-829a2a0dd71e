import {
  Collapse_default
} from "./chunk-JNVPO6EQ.js";
import {
  createSvgIcon
} from "./chunk-2WMRNP5B.js";
import {
  alpha
} from "./chunk-FQ6I3OD7.js";
import {
  styled_default
} from "./chunk-KIJLS2TV.js";
import {
  useThemeProps
} from "./chunk-ZYUAWKJJ.js";
import {
  resolveComponentProps,
  useSlotProps
} from "./chunk-P6LQB3KR.js";
import {
  elementTypeAcceptingRef_default,
  unsupportedProp
} from "./chunk-EXKCK6CI.js";
import {
  useForkRef
} from "./chunk-RCV74CT4.js";
import {
  clsx_default
} from "./chunk-YV3COZNF.js";
import {
  composeClasses,
  generateUtilityClass,
  generateUtilityClasses
} from "./chunk-EH52VBW6.js";
import {
  require_prop_types
} from "./chunk-MDE6ZET7.js";
import {
  _objectWithoutPropertiesLoose
} from "./chunk-OBSDRUBD.js";
import {
  require_jsx_runtime
} from "./chunk-D4DBS43D.js";
import {
  _extends,
  init_extends
} from "./chunk-4GAI7T4A.js";
import {
  require_react
} from "./chunk-R56R2YIZ.js";
import {
  __toESM
} from "./chunk-BYPFWIQ6.js";

// node_modules/@mui/x-tree-view/TreeItem/TreeItem.js
init_extends();
var React5 = __toESM(require_react());
var import_prop_types3 = __toESM(require_prop_types());

// node_modules/@mui/x-tree-view/TreeItem/TreeItemContent.js
init_extends();
var React3 = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());

// node_modules/@mui/x-tree-view/internals/TreeViewProvider/useTreeViewContext.js
var React2 = __toESM(require_react());

// node_modules/@mui/x-tree-view/internals/TreeViewProvider/TreeViewContext.js
var React = __toESM(require_react());
var TreeViewContext = React.createContext(null);
if (true) {
  TreeViewContext.displayName = "TreeViewContext";
}

// node_modules/@mui/x-tree-view/internals/TreeViewProvider/useTreeViewContext.js
var useTreeViewContext = () => {
  const context = React2.useContext(TreeViewContext);
  if (context == null) {
    throw new Error(["MUI X: Could not find the Tree View context.", "It looks like you rendered your component outside of a SimpleTreeView or RichTreeView parent component.", "This can also happen if you are bundling multiple versions of the Tree View."].join("\n"));
  }
  return context;
};

// node_modules/@mui/x-tree-view/TreeItem/useTreeItemState.js
function useTreeItemState(itemId) {
  const {
    instance,
    selection: {
      multiSelect
    }
  } = useTreeViewContext();
  const expandable = instance.isItemExpandable(itemId);
  const expanded = instance.isItemExpanded(itemId);
  const focused = instance.isItemFocused(itemId);
  const selected = instance.isItemSelected(itemId);
  const disabled = instance.isItemDisabled(itemId);
  const handleExpansion = (event) => {
    if (!disabled) {
      if (!focused) {
        instance.focusItem(event, itemId);
      }
      const multiple = multiSelect && (event.shiftKey || event.ctrlKey || event.metaKey);
      if (expandable && !(multiple && instance.isItemExpanded(itemId))) {
        instance.toggleItemExpansion(event, itemId);
      }
    }
  };
  const handleSelection = (event) => {
    if (!disabled) {
      if (!focused) {
        instance.focusItem(event, itemId);
      }
      const multiple = multiSelect && (event.shiftKey || event.ctrlKey || event.metaKey);
      if (multiple) {
        if (event.shiftKey) {
          instance.selectRange(event, {
            end: itemId
          });
        } else {
          instance.selectItem(event, itemId, true);
        }
      } else {
        instance.selectItem(event, itemId);
      }
    }
  };
  const preventSelection = (event) => {
    if (event.shiftKey || event.ctrlKey || event.metaKey || disabled) {
      event.preventDefault();
    }
  };
  return {
    disabled,
    expanded,
    selected,
    focused,
    handleExpansion,
    handleSelection,
    preventSelection
  };
}

// node_modules/@mui/x-tree-view/TreeItem/TreeItemContent.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var _excluded = ["classes", "className", "displayIcon", "expansionIcon", "icon", "label", "itemId", "onClick", "onMouseDown"];
var TreeItemContent = React3.forwardRef(function TreeItemContent2(props, ref) {
  const {
    classes,
    className,
    displayIcon,
    expansionIcon,
    icon: iconProp,
    label,
    itemId,
    onClick,
    onMouseDown
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded);
  const {
    disabled,
    expanded,
    selected,
    focused,
    handleExpansion,
    handleSelection,
    preventSelection
  } = useTreeItemState(itemId);
  const icon = iconProp || expansionIcon || displayIcon;
  const handleMouseDown = (event) => {
    preventSelection(event);
    if (onMouseDown) {
      onMouseDown(event);
    }
  };
  const handleClick = (event) => {
    handleExpansion(event);
    handleSelection(event);
    if (onClick) {
      onClick(event);
    }
  };
  return (
    /* eslint-disable-next-line jsx-a11y/click-events-have-key-events,jsx-a11y/no-static-element-interactions -- Key event is handled by the TreeView */
    (0, import_jsx_runtime2.jsxs)("div", _extends({}, other, {
      className: clsx_default(className, classes.root, expanded && classes.expanded, selected && classes.selected, focused && classes.focused, disabled && classes.disabled),
      onClick: handleClick,
      onMouseDown: handleMouseDown,
      ref,
      children: [(0, import_jsx_runtime.jsx)("div", {
        className: classes.iconContainer,
        children: icon
      }), (0, import_jsx_runtime.jsx)("div", {
        className: classes.label,
        children: label
      })]
    }))
  );
});
true ? TreeItemContent.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types.default.object.isRequired,
  className: import_prop_types.default.string,
  /**
   * The icon to display next to the tree item's label. Either a parent or end icon.
   */
  displayIcon: import_prop_types.default.node,
  /**
   * The icon to display next to the tree item's label. Either an expansion or collapse icon.
   */
  expansionIcon: import_prop_types.default.node,
  /**
   * The icon to display next to the tree item's label.
   */
  icon: import_prop_types.default.node,
  /**
   * The id of the item.
   */
  itemId: import_prop_types.default.string.isRequired,
  /**
   * The tree item label.
   */
  label: import_prop_types.default.node
} : void 0;

// node_modules/@mui/x-tree-view/TreeItem/treeItemClasses.js
function getTreeItemUtilityClass(slot) {
  return generateUtilityClass("MuiTreeItem", slot);
}
var treeItemClasses = generateUtilityClasses("MuiTreeItem", ["root", "groupTransition", "content", "expanded", "selected", "focused", "disabled", "iconContainer", "label"]);

// node_modules/@mui/x-tree-view/icons/icons.js
var React4 = __toESM(require_react());
var import_jsx_runtime3 = __toESM(require_jsx_runtime());
var TreeViewExpandIcon = createSvgIcon((0, import_jsx_runtime3.jsx)("path", {
  d: "M10 6 8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"
}), "TreeViewExpandIcon");
var TreeViewCollapseIcon = createSvgIcon((0, import_jsx_runtime3.jsx)("path", {
  d: "M16.59 8.59 12 13.17 7.41 8.59 6 10l6 6 6-6z"
}), "TreeViewCollapseIcon");

// node_modules/@mui/x-tree-view/TreeItem2Provider/TreeItem2Provider.js
var import_prop_types2 = __toESM(require_prop_types());
function TreeItem2Provider(props) {
  const {
    children,
    itemId
  } = props;
  const {
    wrapItem
  } = useTreeViewContext();
  return wrapItem({
    children,
    itemId
  });
}
TreeItem2Provider.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  children: import_prop_types2.default.node,
  itemId: import_prop_types2.default.string.isRequired
};

// node_modules/@mui/x-tree-view/TreeItem/TreeItem.js
var import_jsx_runtime4 = __toESM(require_jsx_runtime());
var import_jsx_runtime5 = __toESM(require_jsx_runtime());
var _excluded2 = ["children", "className", "slots", "slotProps", "ContentComponent", "ContentProps", "itemId", "id", "label", "onClick", "onMouseDown", "onFocus", "onBlur", "onKeyDown"];
var _excluded22 = ["ownerState"];
var _excluded3 = ["ownerState"];
var _excluded4 = ["ownerState"];
var useUtilityClasses = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"],
    content: ["content"],
    expanded: ["expanded"],
    selected: ["selected"],
    focused: ["focused"],
    disabled: ["disabled"],
    iconContainer: ["iconContainer"],
    label: ["label"],
    groupTransition: ["groupTransition"]
  };
  return composeClasses(slots, getTreeItemUtilityClass, classes);
};
var TreeItemRoot = styled_default("li", {
  name: "MuiTreeItem",
  slot: "Root",
  overridesResolver: (props, styles) => styles.root
})({
  listStyle: "none",
  margin: 0,
  padding: 0,
  outline: 0
});
var StyledTreeItemContent = styled_default(TreeItemContent, {
  name: "MuiTreeItem",
  slot: "Content",
  overridesResolver: (props, styles) => {
    return [styles.content, styles.iconContainer && {
      [`& .${treeItemClasses.iconContainer}`]: styles.iconContainer
    }, styles.label && {
      [`& .${treeItemClasses.label}`]: styles.label
    }];
  }
})(({
  theme
}) => ({
  padding: theme.spacing(0.5, 1),
  borderRadius: theme.shape.borderRadius,
  width: "100%",
  boxSizing: "border-box",
  // prevent width + padding to overflow
  display: "flex",
  alignItems: "center",
  gap: theme.spacing(1),
  cursor: "pointer",
  WebkitTapHighlightColor: "transparent",
  "&:hover": {
    backgroundColor: (theme.vars || theme).palette.action.hover,
    // Reset on touch devices, it doesn't add specificity
    "@media (hover: none)": {
      backgroundColor: "transparent"
    }
  },
  [`&.${treeItemClasses.disabled}`]: {
    opacity: (theme.vars || theme).palette.action.disabledOpacity,
    backgroundColor: "transparent"
  },
  [`&.${treeItemClasses.focused}`]: {
    backgroundColor: (theme.vars || theme).palette.action.focus
  },
  [`&.${treeItemClasses.selected}`]: {
    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.selectedOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity),
    "&:hover": {
      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.hoverOpacity}))` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.hoverOpacity),
      // Reset on touch devices, it doesn't add specificity
      "@media (hover: none)": {
        backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.selectedOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity)
      }
    },
    [`&.${treeItemClasses.focused}`]: {
      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)
    }
  },
  [`& .${treeItemClasses.iconContainer}`]: {
    width: 16,
    display: "flex",
    flexShrink: 0,
    justifyContent: "center",
    "& svg": {
      fontSize: 18
    }
  },
  [`& .${treeItemClasses.label}`]: _extends({
    width: "100%",
    boxSizing: "border-box",
    // prevent width + padding to overflow
    // fixes overflow - see https://github.com/mui/material-ui/issues/27372
    minWidth: 0,
    position: "relative"
  }, theme.typography.body1)
}));
var TreeItemGroup = styled_default(Collapse_default, {
  name: "MuiTreeItem",
  slot: "GroupTransition",
  overridesResolver: (props, styles) => styles.groupTransition
})({
  margin: 0,
  padding: 0,
  paddingLeft: 12
});
var TreeItem = React5.forwardRef(function TreeItem2(inProps, inRef) {
  const {
    icons: contextIcons,
    runItemPlugins,
    selection: {
      multiSelect
    },
    disabledItemsFocusable,
    instance
  } = useTreeViewContext();
  const props = useThemeProps({
    props: inProps,
    name: "MuiTreeItem"
  });
  const {
    children,
    className,
    slots: inSlots,
    slotProps: inSlotProps,
    ContentComponent = TreeItemContent,
    ContentProps,
    itemId,
    id,
    label,
    onClick,
    onMouseDown,
    onBlur,
    onKeyDown
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded2);
  const {
    contentRef,
    rootRef
  } = runItemPlugins(props);
  const handleRootRef = useForkRef(inRef, rootRef);
  const handleContentRef = useForkRef(ContentProps == null ? void 0 : ContentProps.ref, contentRef);
  const slots = {
    expandIcon: (inSlots == null ? void 0 : inSlots.expandIcon) ?? contextIcons.slots.expandIcon ?? TreeViewExpandIcon,
    collapseIcon: (inSlots == null ? void 0 : inSlots.collapseIcon) ?? contextIcons.slots.collapseIcon ?? TreeViewCollapseIcon,
    endIcon: (inSlots == null ? void 0 : inSlots.endIcon) ?? contextIcons.slots.endIcon,
    icon: inSlots == null ? void 0 : inSlots.icon,
    groupTransition: inSlots == null ? void 0 : inSlots.groupTransition
  };
  const isExpandable = (reactChildren) => {
    if (Array.isArray(reactChildren)) {
      return reactChildren.length > 0 && reactChildren.some(isExpandable);
    }
    return Boolean(reactChildren);
  };
  const expandable = isExpandable(children);
  const expanded = instance.isItemExpanded(itemId);
  const focused = instance.isItemFocused(itemId);
  const selected = instance.isItemSelected(itemId);
  const disabled = instance.isItemDisabled(itemId);
  const ownerState = _extends({}, props, {
    expanded,
    focused,
    selected,
    disabled
  });
  const classes = useUtilityClasses(ownerState);
  const GroupTransition = slots.groupTransition ?? void 0;
  const groupTransitionProps = useSlotProps({
    elementType: GroupTransition,
    ownerState: {},
    externalSlotProps: inSlotProps == null ? void 0 : inSlotProps.groupTransition,
    additionalProps: {
      unmountOnExit: true,
      in: expanded,
      component: "ul",
      role: "group"
    },
    className: classes.groupTransition
  });
  const ExpansionIcon = expanded ? slots.collapseIcon : slots.expandIcon;
  const _useSlotProps = useSlotProps({
    elementType: ExpansionIcon,
    ownerState: {},
    externalSlotProps: (tempOwnerState) => {
      if (expanded) {
        return _extends({}, resolveComponentProps(contextIcons.slotProps.collapseIcon, tempOwnerState), resolveComponentProps(inSlotProps == null ? void 0 : inSlotProps.collapseIcon, tempOwnerState));
      }
      return _extends({}, resolveComponentProps(contextIcons.slotProps.expandIcon, tempOwnerState), resolveComponentProps(inSlotProps == null ? void 0 : inSlotProps.expandIcon, tempOwnerState));
    }
  }), expansionIconProps = _objectWithoutPropertiesLoose(_useSlotProps, _excluded22);
  const expansionIcon = expandable && !!ExpansionIcon ? (0, import_jsx_runtime4.jsx)(ExpansionIcon, _extends({}, expansionIconProps)) : null;
  const DisplayIcon = expandable ? void 0 : slots.endIcon;
  const _useSlotProps2 = useSlotProps({
    elementType: DisplayIcon,
    ownerState: {},
    externalSlotProps: (tempOwnerState) => {
      if (expandable) {
        return {};
      }
      return _extends({}, resolveComponentProps(contextIcons.slotProps.endIcon, tempOwnerState), resolveComponentProps(inSlotProps == null ? void 0 : inSlotProps.endIcon, tempOwnerState));
    }
  }), displayIconProps = _objectWithoutPropertiesLoose(_useSlotProps2, _excluded3);
  const displayIcon = DisplayIcon ? (0, import_jsx_runtime4.jsx)(DisplayIcon, _extends({}, displayIconProps)) : null;
  const Icon = slots.icon;
  const _useSlotProps3 = useSlotProps({
    elementType: Icon,
    ownerState: {},
    externalSlotProps: inSlotProps == null ? void 0 : inSlotProps.icon
  }), iconProps = _objectWithoutPropertiesLoose(_useSlotProps3, _excluded4);
  const icon = Icon ? (0, import_jsx_runtime4.jsx)(Icon, _extends({}, iconProps)) : null;
  let ariaSelected;
  if (multiSelect) {
    ariaSelected = selected;
  } else if (selected) {
    ariaSelected = true;
  }
  function handleFocus(event) {
    const canBeFocused = !disabled || disabledItemsFocusable;
    if (!focused && canBeFocused && event.currentTarget === event.target) {
      instance.focusItem(event, itemId);
    }
  }
  function handleBlur(event) {
    onBlur == null ? void 0 : onBlur(event);
    instance.removeFocusedItem();
  }
  const handleKeyDown = (event) => {
    onKeyDown == null ? void 0 : onKeyDown(event);
    instance.handleItemKeyDown(event, itemId);
  };
  const idAttribute = instance.getTreeItemIdAttribute(itemId, id);
  const tabIndex = instance.canItemBeTabbed(itemId) ? 0 : -1;
  return (0, import_jsx_runtime4.jsx)(TreeItem2Provider, {
    itemId,
    children: (0, import_jsx_runtime5.jsxs)(TreeItemRoot, _extends({
      className: clsx_default(classes.root, className),
      role: "treeitem",
      "aria-expanded": expandable ? expanded : void 0,
      "aria-selected": ariaSelected,
      "aria-disabled": disabled || void 0,
      id: idAttribute,
      tabIndex
    }, other, {
      ownerState,
      onFocus: handleFocus,
      onBlur: handleBlur,
      onKeyDown: handleKeyDown,
      ref: handleRootRef,
      children: [(0, import_jsx_runtime4.jsx)(StyledTreeItemContent, _extends({
        as: ContentComponent,
        classes: {
          root: classes.content,
          expanded: classes.expanded,
          selected: classes.selected,
          focused: classes.focused,
          disabled: classes.disabled,
          iconContainer: classes.iconContainer,
          label: classes.label
        },
        label,
        itemId,
        onClick,
        onMouseDown,
        icon,
        expansionIcon,
        displayIcon,
        ownerState
      }, ContentProps, {
        ref: handleContentRef
      })), children && (0, import_jsx_runtime4.jsx)(TreeItemGroup, _extends({
        as: GroupTransition
      }, groupTransitionProps, {
        children
      }))]
    }))
  });
});
true ? TreeItem.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The content of the component.
   */
  children: import_prop_types3.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types3.default.object,
  className: import_prop_types3.default.string,
  /**
   * The component used to render the content of the item.
   * @default TreeItemContent
   */
  ContentComponent: elementTypeAcceptingRef_default,
  /**
   * Props applied to ContentComponent.
   */
  ContentProps: import_prop_types3.default.object,
  /**
   * If `true`, the item is disabled.
   * @default false
   */
  disabled: import_prop_types3.default.bool,
  /**
   * The id of the item.
   */
  itemId: import_prop_types3.default.string.isRequired,
  /**
   * The tree item label.
   */
  label: import_prop_types3.default.node,
  /**
   * This prop isn't supported.
   * Use the `onItemFocus` callback on the tree if you need to monitor a item's focus.
   */
  onFocus: unsupportedProp,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types3.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types3.default.object,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types3.default.oneOfType([import_prop_types3.default.arrayOf(import_prop_types3.default.oneOfType([import_prop_types3.default.func, import_prop_types3.default.object, import_prop_types3.default.bool])), import_prop_types3.default.func, import_prop_types3.default.object])
} : void 0;

export {
  TreeViewContext,
  useTreeViewContext,
  useTreeItemState,
  TreeItemContent,
  getTreeItemUtilityClass,
  treeItemClasses,
  TreeViewExpandIcon,
  TreeViewCollapseIcon,
  TreeItem2Provider,
  TreeItem
};
//# sourceMappingURL=chunk-ODYFQNIF.js.map
