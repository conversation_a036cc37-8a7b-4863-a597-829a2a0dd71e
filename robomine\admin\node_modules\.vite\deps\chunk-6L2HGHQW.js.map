{"version": 3, "sources": ["../../@mui/material/SpeedDialAction/SpeedDialAction.js", "../../@mui/material/SpeedDialAction/speedDialActionClasses.js"], "sourcesContent": ["'use client';\n\n// @inheritedComponent Tooltip\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"delay\", \"FabProps\", \"icon\", \"id\", \"open\", \"TooltipClasses\", \"tooltipOpen\", \"tooltipPlacement\", \"tooltipTitle\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { emphasize } from '@mui/system/colorManipulator';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport Fab from '../Fab';\nimport Tooltip from '../Tooltip';\nimport capitalize from '../utils/capitalize';\nimport speedDialActionClasses, { getSpeedDialActionUtilityClass } from './speedDialActionClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    open,\n    tooltipPlacement,\n    classes\n  } = ownerState;\n  const slots = {\n    fab: ['fab', !open && 'fabClosed'],\n    staticTooltip: ['staticTooltip', `tooltipPlacement${capitalize(tooltipPlacement)}`, !open && 'staticTooltipClosed'],\n    staticTooltipLabel: ['staticTooltipLabel']\n  };\n  return composeClasses(slots, getSpeedDialActionUtilityClass, classes);\n};\nconst SpeedDialActionFab = styled(Fab, {\n  name: 'MuiSpeedDialAction',\n  slot: 'Fab',\n  skipVariantsResolver: false,\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.fab, !ownerState.open && styles.fabClosed];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  margin: 8,\n  color: (theme.vars || theme).palette.text.secondary,\n  backgroundColor: (theme.vars || theme).palette.background.paper,\n  '&:hover': {\n    backgroundColor: theme.vars ? theme.vars.palette.SpeedDialAction.fabHoverBg : emphasize(theme.palette.background.paper, 0.15)\n  },\n  transition: `${theme.transitions.create('transform', {\n    duration: theme.transitions.duration.shorter\n  })}, opacity 0.8s`,\n  opacity: 1\n}, !ownerState.open && {\n  opacity: 0,\n  transform: 'scale(0)'\n}));\nconst SpeedDialActionStaticTooltip = styled('span', {\n  name: 'MuiSpeedDialAction',\n  slot: 'StaticTooltip',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.staticTooltip, !ownerState.open && styles.staticTooltipClosed, styles[`tooltipPlacement${capitalize(ownerState.tooltipPlacement)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => ({\n  position: 'relative',\n  display: 'flex',\n  alignItems: 'center',\n  [`& .${speedDialActionClasses.staticTooltipLabel}`]: _extends({\n    transition: theme.transitions.create(['transform', 'opacity'], {\n      duration: theme.transitions.duration.shorter\n    }),\n    opacity: 1\n  }, !ownerState.open && {\n    opacity: 0,\n    transform: 'scale(0.5)'\n  }, ownerState.tooltipPlacement === 'left' && {\n    transformOrigin: '100% 50%',\n    right: '100%',\n    marginRight: 8\n  }, ownerState.tooltipPlacement === 'right' && {\n    transformOrigin: '0% 50%',\n    left: '100%',\n    marginLeft: 8\n  })\n}));\nconst SpeedDialActionStaticTooltipLabel = styled('span', {\n  name: 'MuiSpeedDialAction',\n  slot: 'StaticTooltipLabel',\n  overridesResolver: (props, styles) => styles.staticTooltipLabel\n})(({\n  theme\n}) => _extends({\n  position: 'absolute'\n}, theme.typography.body1, {\n  backgroundColor: (theme.vars || theme).palette.background.paper,\n  borderRadius: (theme.vars || theme).shape.borderRadius,\n  boxShadow: (theme.vars || theme).shadows[1],\n  color: (theme.vars || theme).palette.text.secondary,\n  padding: '4px 16px',\n  wordBreak: 'keep-all'\n}));\nconst SpeedDialAction = /*#__PURE__*/React.forwardRef(function SpeedDialAction(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiSpeedDialAction'\n  });\n  const {\n      className,\n      delay = 0,\n      FabProps = {},\n      icon,\n      id,\n      open,\n      TooltipClasses,\n      tooltipOpen: tooltipOpenProp = false,\n      tooltipPlacement = 'left',\n      tooltipTitle\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    tooltipPlacement\n  });\n  const classes = useUtilityClasses(ownerState);\n  const [tooltipOpen, setTooltipOpen] = React.useState(tooltipOpenProp);\n  const handleTooltipClose = () => {\n    setTooltipOpen(false);\n  };\n  const handleTooltipOpen = () => {\n    setTooltipOpen(true);\n  };\n  const transitionStyle = {\n    transitionDelay: `${delay}ms`\n  };\n  const fab = /*#__PURE__*/_jsx(SpeedDialActionFab, _extends({\n    size: \"small\",\n    className: clsx(classes.fab, className),\n    tabIndex: -1,\n    role: \"menuitem\",\n    ownerState: ownerState\n  }, FabProps, {\n    style: _extends({}, transitionStyle, FabProps.style),\n    children: icon\n  }));\n  if (tooltipOpenProp) {\n    return /*#__PURE__*/_jsxs(SpeedDialActionStaticTooltip, _extends({\n      id: id,\n      ref: ref,\n      className: classes.staticTooltip,\n      ownerState: ownerState\n    }, other, {\n      children: [/*#__PURE__*/_jsx(SpeedDialActionStaticTooltipLabel, {\n        style: transitionStyle,\n        id: `${id}-label`,\n        className: classes.staticTooltipLabel,\n        ownerState: ownerState,\n        children: tooltipTitle\n      }), /*#__PURE__*/React.cloneElement(fab, {\n        'aria-labelledby': `${id}-label`\n      })]\n    }));\n  }\n  if (!open && tooltipOpen) {\n    setTooltipOpen(false);\n  }\n  return /*#__PURE__*/_jsx(Tooltip, _extends({\n    id: id,\n    ref: ref,\n    title: tooltipTitle,\n    placement: tooltipPlacement,\n    onClose: handleTooltipClose,\n    onOpen: handleTooltipOpen,\n    open: open && tooltipOpen,\n    classes: TooltipClasses\n  }, other, {\n    children: fab\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? SpeedDialAction.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Adds a transition delay, to allow a series of SpeedDialActions to be animated.\n   * @default 0\n   */\n  delay: PropTypes.number,\n  /**\n   * Props applied to the [`Fab`](/material-ui/api/fab/) component.\n   * @default {}\n   */\n  FabProps: PropTypes.object,\n  /**\n   * The icon to display in the SpeedDial Fab.\n   */\n  icon: PropTypes.node,\n  /**\n   * This prop is used to help implement the accessibility logic.\n   * If you don't provide this prop. It falls back to a randomly generated id.\n   */\n  id: PropTypes.string,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * `classes` prop applied to the [`Tooltip`](/material-ui/api/tooltip/) element.\n   */\n  TooltipClasses: PropTypes.object,\n  /**\n   * Make the tooltip always visible when the SpeedDial is open.\n   * @default false\n   */\n  tooltipOpen: PropTypes.bool,\n  /**\n   * Placement of the tooltip.\n   * @default 'left'\n   */\n  tooltipPlacement: PropTypes.oneOf(['bottom-end', 'bottom-start', 'bottom', 'left-end', 'left-start', 'left', 'right-end', 'right-start', 'right', 'top-end', 'top-start', 'top']),\n  /**\n   * Label to display in the tooltip.\n   */\n  tooltipTitle: PropTypes.node\n} : void 0;\nexport default SpeedDialAction;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSpeedDialActionUtilityClass(slot) {\n  return generateUtilityClass('MuiSpeedDialAction', slot);\n}\nconst speedDialActionClasses = generateUtilityClasses('MuiSpeedDialAction', ['fab', 'fabClosed', 'staticTooltip', 'staticTooltipClosed', 'staticTooltipLabel', 'tooltipPlacementLeft', 'tooltipPlacementRight']);\nexport default speedDialActionClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA;AAEA,YAAuB;AACvB,wBAAsB;AAGtB,8BAA0B;;;ACRnB,SAAS,+BAA+B,MAAM;AACnD,SAAO,qBAAqB,sBAAsB,IAAI;AACxD;AACA,IAAM,yBAAyB,uBAAuB,sBAAsB,CAAC,OAAO,aAAa,iBAAiB,uBAAuB,sBAAsB,wBAAwB,uBAAuB,CAAC;AAC/M,IAAO,iCAAQ;;;ADWf,yBAA4B;AAC5B,IAAAA,sBAA8B;AAb9B,IAAM,YAAY,CAAC,aAAa,SAAS,YAAY,QAAQ,MAAM,QAAQ,kBAAkB,eAAe,oBAAoB,cAAc;AAc9I,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,KAAK,CAAC,OAAO,CAAC,QAAQ,WAAW;AAAA,IACjC,eAAe,CAAC,iBAAiB,mBAAmB,mBAAW,gBAAgB,CAAC,IAAI,CAAC,QAAQ,qBAAqB;AAAA,IAClH,oBAAoB,CAAC,oBAAoB;AAAA,EAC3C;AACA,SAAO,eAAe,OAAO,gCAAgC,OAAO;AACtE;AACA,IAAM,qBAAqB,eAAO,aAAK;AAAA,EACrC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,sBAAsB;AAAA,EACtB,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,KAAK,CAAC,WAAW,QAAQ,OAAO,SAAS;AAAA,EAC1D;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AAAA,EACA;AACF,MAAM,SAAS;AAAA,EACb,QAAQ;AAAA,EACR,QAAQ,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,EAC1C,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,WAAW;AAAA,EAC1D,WAAW;AAAA,IACT,iBAAiB,MAAM,OAAO,MAAM,KAAK,QAAQ,gBAAgB,iBAAa,mCAAU,MAAM,QAAQ,WAAW,OAAO,IAAI;AAAA,EAC9H;AAAA,EACA,YAAY,GAAG,MAAM,YAAY,OAAO,aAAa;AAAA,IACnD,UAAU,MAAM,YAAY,SAAS;AAAA,EACvC,CAAC,CAAC;AAAA,EACF,SAAS;AACX,GAAG,CAAC,WAAW,QAAQ;AAAA,EACrB,SAAS;AAAA,EACT,WAAW;AACb,CAAC,CAAC;AACF,IAAM,+BAA+B,eAAO,QAAQ;AAAA,EAClD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,eAAe,CAAC,WAAW,QAAQ,OAAO,qBAAqB,OAAO,mBAAmB,mBAAW,WAAW,gBAAgB,CAAC,EAAE,CAAC;AAAA,EACpJ;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AAAA,EACA;AACF,OAAO;AAAA,EACL,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,CAAC,MAAM,+BAAuB,kBAAkB,EAAE,GAAG,SAAS;AAAA,IAC5D,YAAY,MAAM,YAAY,OAAO,CAAC,aAAa,SAAS,GAAG;AAAA,MAC7D,UAAU,MAAM,YAAY,SAAS;AAAA,IACvC,CAAC;AAAA,IACD,SAAS;AAAA,EACX,GAAG,CAAC,WAAW,QAAQ;AAAA,IACrB,SAAS;AAAA,IACT,WAAW;AAAA,EACb,GAAG,WAAW,qBAAqB,UAAU;AAAA,IAC3C,iBAAiB;AAAA,IACjB,OAAO;AAAA,IACP,aAAa;AAAA,EACf,GAAG,WAAW,qBAAqB,WAAW;AAAA,IAC5C,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,YAAY;AAAA,EACd,CAAC;AACH,EAAE;AACF,IAAM,oCAAoC,eAAO,QAAQ;AAAA,EACvD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE,CAAC;AAAA,EACF;AACF,MAAM,SAAS;AAAA,EACb,UAAU;AACZ,GAAG,MAAM,WAAW,OAAO;AAAA,EACzB,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,WAAW;AAAA,EAC1D,eAAe,MAAM,QAAQ,OAAO,MAAM;AAAA,EAC1C,YAAY,MAAM,QAAQ,OAAO,QAAQ,CAAC;AAAA,EAC1C,QAAQ,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,EAC1C,SAAS;AAAA,EACT,WAAW;AACb,CAAC,CAAC;AACF,IAAM,kBAAqC,iBAAW,SAASC,iBAAgB,SAAS,KAAK;AAC3F,QAAM,QAAQ,cAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,IACR,WAAW,CAAC;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa,kBAAkB;AAAA,IAC/B,mBAAmB;AAAA,IACnB;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,IACrC;AAAA,EACF,CAAC;AACD,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,CAAC,aAAa,cAAc,IAAU,eAAS,eAAe;AACpE,QAAM,qBAAqB,MAAM;AAC/B,mBAAe,KAAK;AAAA,EACtB;AACA,QAAM,oBAAoB,MAAM;AAC9B,mBAAe,IAAI;AAAA,EACrB;AACA,QAAM,kBAAkB;AAAA,IACtB,iBAAiB,GAAG,KAAK;AAAA,EAC3B;AACA,QAAM,UAAmB,mBAAAC,KAAK,oBAAoB,SAAS;AAAA,IACzD,MAAM;AAAA,IACN,WAAW,aAAK,QAAQ,KAAK,SAAS;AAAA,IACtC,UAAU;AAAA,IACV,MAAM;AAAA,IACN;AAAA,EACF,GAAG,UAAU;AAAA,IACX,OAAO,SAAS,CAAC,GAAG,iBAAiB,SAAS,KAAK;AAAA,IACnD,UAAU;AAAA,EACZ,CAAC,CAAC;AACF,MAAI,iBAAiB;AACnB,eAAoB,oBAAAC,MAAM,8BAA8B,SAAS;AAAA,MAC/D;AAAA,MACA;AAAA,MACA,WAAW,QAAQ;AAAA,MACnB;AAAA,IACF,GAAG,OAAO;AAAA,MACR,UAAU,KAAc,mBAAAD,KAAK,mCAAmC;AAAA,QAC9D,OAAO;AAAA,QACP,IAAI,GAAG,EAAE;AAAA,QACT,WAAW,QAAQ;AAAA,QACnB;AAAA,QACA,UAAU;AAAA,MACZ,CAAC,GAAsB,mBAAa,KAAK;AAAA,QACvC,mBAAmB,GAAG,EAAE;AAAA,MAC1B,CAAC,CAAC;AAAA,IACJ,CAAC,CAAC;AAAA,EACJ;AACA,MAAI,CAAC,QAAQ,aAAa;AACxB,mBAAe,KAAK;AAAA,EACtB;AACA,aAAoB,mBAAAA,KAAK,iBAAS,SAAS;AAAA,IACzC;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP,WAAW;AAAA,IACX,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,MAAM,QAAQ;AAAA,IACd,SAAS;AAAA,EACX,GAAG,OAAO;AAAA,IACR,UAAU;AAAA,EACZ,CAAC,CAAC;AACJ,CAAC;AACD,OAAwC,gBAAgB,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQzF,SAAS,kBAAAE,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,MAAM,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhB,IAAI,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAId,MAAM,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIhB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAItJ,gBAAgB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1B,aAAa,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,kBAAkB,kBAAAA,QAAU,MAAM,CAAC,cAAc,gBAAgB,UAAU,YAAY,cAAc,QAAQ,aAAa,eAAe,SAAS,WAAW,aAAa,KAAK,CAAC;AAAA;AAAA;AAAA;AAAA,EAIhL,cAAc,kBAAAA,QAAU;AAC1B,IAAI;AACJ,IAAO,0BAAQ;", "names": ["import_jsx_runtime", "SpeedDialAction", "_jsx", "_jsxs", "PropTypes"]}