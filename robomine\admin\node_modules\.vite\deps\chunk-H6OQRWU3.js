import {
  Step<PERSON><PERSON>l_default
} from "./chunk-WPA7IU4M.js";
import {
  StepContext_default,
  StepperContext_default
} from "./chunk-SJNDSUFZ.js";
import {
  ButtonBase_default
} from "./chunk-IHB3C6I2.js";
import {
  isMuiElement_default
} from "./chunk-P3F2W34V.js";
import {
  styled_default
} from "./chunk-KIJLS2TV.js";
import {
  useThemeProps
} from "./chunk-ZYUAWKJJ.js";
import {
  clsx_default
} from "./chunk-YV3COZNF.js";
import {
  composeClasses,
  generateUtilityClass,
  generateUtilityClasses
} from "./chunk-EH52VBW6.js";
import {
  require_prop_types
} from "./chunk-MDE6ZET7.js";
import {
  _objectWithoutPropertiesLoose
} from "./chunk-OBSDRUBD.js";
import {
  require_jsx_runtime
} from "./chunk-D4DBS43D.js";
import {
  _extends,
  init_extends
} from "./chunk-4GAI7T4A.js";
import {
  require_react
} from "./chunk-R56R2YIZ.js";
import {
  __toESM
} from "./chunk-BYPFWIQ6.js";

// node_modules/@mui/material/StepButton/StepButton.js
init_extends();
var React = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());

// node_modules/@mui/material/StepButton/stepButtonClasses.js
function getStepButtonUtilityClass(slot) {
  return generateUtilityClass("MuiStepButton", slot);
}
var stepButtonClasses = generateUtilityClasses("MuiStepButton", ["root", "horizontal", "vertical", "touchRipple"]);
var stepButtonClasses_default = stepButtonClasses;

// node_modules/@mui/material/StepButton/StepButton.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var _excluded = ["children", "className", "icon", "optional"];
var useUtilityClasses = (ownerState) => {
  const {
    classes,
    orientation
  } = ownerState;
  const slots = {
    root: ["root", orientation],
    touchRipple: ["touchRipple"]
  };
  return composeClasses(slots, getStepButtonUtilityClass, classes);
};
var StepButtonRoot = styled_default(ButtonBase_default, {
  name: "MuiStepButton",
  slot: "Root",
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [{
      [`& .${stepButtonClasses_default.touchRipple}`]: styles.touchRipple
    }, styles.root, styles[ownerState.orientation]];
  }
})(({
  ownerState
}) => _extends({
  width: "100%",
  padding: "24px 16px",
  margin: "-24px -16px",
  boxSizing: "content-box"
}, ownerState.orientation === "vertical" && {
  justifyContent: "flex-start",
  padding: "8px",
  margin: "-8px"
}, {
  [`& .${stepButtonClasses_default.touchRipple}`]: {
    color: "rgba(0, 0, 0, 0.3)"
  }
}));
var StepButton = React.forwardRef(function StepButton2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiStepButton"
  });
  const {
    children,
    className,
    icon,
    optional
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded);
  const {
    disabled,
    active
  } = React.useContext(StepContext_default);
  const {
    orientation
  } = React.useContext(StepperContext_default);
  const ownerState = _extends({}, props, {
    orientation
  });
  const classes = useUtilityClasses(ownerState);
  const childProps = {
    icon,
    optional
  };
  const child = isMuiElement_default(children, ["StepLabel"]) ? React.cloneElement(children, childProps) : (0, import_jsx_runtime.jsx)(StepLabel_default, _extends({}, childProps, {
    children
  }));
  return (0, import_jsx_runtime.jsx)(StepButtonRoot, _extends({
    focusRipple: true,
    disabled,
    TouchRippleProps: {
      className: classes.touchRipple
    },
    className: clsx_default(classes.root, className),
    ref,
    ownerState,
    "aria-current": active ? "step" : void 0
  }, other, {
    children: child
  }));
});
true ? StepButton.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * Can be a `StepLabel` or a node to place inside `StepLabel` as children.
   */
  children: import_prop_types.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types.default.object,
  /**
   * @ignore
   */
  className: import_prop_types.default.string,
  /**
   * The icon displayed by the step label.
   */
  icon: import_prop_types.default.node,
  /**
   * The optional node to display.
   */
  optional: import_prop_types.default.node,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types.default.oneOfType([import_prop_types.default.arrayOf(import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object, import_prop_types.default.bool])), import_prop_types.default.func, import_prop_types.default.object])
} : void 0;
var StepButton_default = StepButton;

export {
  getStepButtonUtilityClass,
  stepButtonClasses_default,
  StepButton_default
};
//# sourceMappingURL=chunk-H6OQRWU3.js.map
