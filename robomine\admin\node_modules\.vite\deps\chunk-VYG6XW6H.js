import {
  StepContext_default,
  StepperContext_default
} from "./chunk-SJNDSUFZ.js";
import {
  Collapse_default
} from "./chunk-JNVPO6EQ.js";
import {
  styled_default
} from "./chunk-KIJLS2TV.js";
import {
  useThemeProps
} from "./chunk-ZYUAWKJJ.js";
import {
  clsx_default
} from "./chunk-YV3COZNF.js";
import {
  composeClasses,
  generateUtilityClass,
  generateUtilityClasses
} from "./chunk-EH52VBW6.js";
import {
  require_prop_types
} from "./chunk-MDE6ZET7.js";
import {
  _objectWithoutPropertiesLoose
} from "./chunk-OBSDRUBD.js";
import {
  require_jsx_runtime
} from "./chunk-D4DBS43D.js";
import {
  _extends,
  init_extends
} from "./chunk-4GAI7T4A.js";
import {
  require_react
} from "./chunk-R56R2YIZ.js";
import {
  __toESM
} from "./chunk-BYPFWIQ6.js";

// node_modules/@mui/material/StepContent/StepContent.js
init_extends();
var React = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());

// node_modules/@mui/material/StepContent/stepContentClasses.js
function getStepContentUtilityClass(slot) {
  return generateUtilityClass("MuiStepContent", slot);
}
var stepContentClasses = generateUtilityClasses("MuiStepContent", ["root", "last", "transition"]);
var stepContentClasses_default = stepContentClasses;

// node_modules/@mui/material/StepContent/StepContent.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var _excluded = ["children", "className", "TransitionComponent", "transitionDuration", "TransitionProps"];
var useUtilityClasses = (ownerState) => {
  const {
    classes,
    last
  } = ownerState;
  const slots = {
    root: ["root", last && "last"],
    transition: ["transition"]
  };
  return composeClasses(slots, getStepContentUtilityClass, classes);
};
var StepContentRoot = styled_default("div", {
  name: "MuiStepContent",
  slot: "Root",
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [styles.root, ownerState.last && styles.last];
  }
})(({
  ownerState,
  theme
}) => _extends({
  marginLeft: 12,
  // half icon
  paddingLeft: 8 + 12,
  // margin + half icon
  paddingRight: 8,
  borderLeft: theme.vars ? `1px solid ${theme.vars.palette.StepContent.border}` : `1px solid ${theme.palette.mode === "light" ? theme.palette.grey[400] : theme.palette.grey[600]}`
}, ownerState.last && {
  borderLeft: "none"
}));
var StepContentTransition = styled_default(Collapse_default, {
  name: "MuiStepContent",
  slot: "Transition",
  overridesResolver: (props, styles) => styles.transition
})({});
var StepContent = React.forwardRef(function StepContent2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiStepContent"
  });
  const {
    children,
    className,
    TransitionComponent = Collapse_default,
    transitionDuration: transitionDurationProp = "auto",
    TransitionProps
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded);
  const {
    orientation
  } = React.useContext(StepperContext_default);
  const {
    active,
    last,
    expanded
  } = React.useContext(StepContext_default);
  const ownerState = _extends({}, props, {
    last
  });
  const classes = useUtilityClasses(ownerState);
  if (true) {
    if (orientation !== "vertical") {
      console.error("MUI: <StepContent /> is only designed for use with the vertical stepper.");
    }
  }
  let transitionDuration = transitionDurationProp;
  if (transitionDurationProp === "auto" && !TransitionComponent.muiSupportAuto) {
    transitionDuration = void 0;
  }
  return (0, import_jsx_runtime.jsx)(StepContentRoot, _extends({
    className: clsx_default(classes.root, className),
    ref,
    ownerState
  }, other, {
    children: (0, import_jsx_runtime.jsx)(StepContentTransition, _extends({
      as: TransitionComponent,
      in: active || expanded,
      className: classes.transition,
      ownerState,
      timeout: transitionDuration,
      unmountOnExit: true
    }, TransitionProps, {
      children
    }))
  }));
});
true ? StepContent.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The content of the component.
   */
  children: import_prop_types.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types.default.object,
  /**
   * @ignore
   */
  className: import_prop_types.default.string,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types.default.oneOfType([import_prop_types.default.arrayOf(import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object, import_prop_types.default.bool])), import_prop_types.default.func, import_prop_types.default.object]),
  /**
   * The component used for the transition.
   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.
   * @default Collapse
   */
  TransitionComponent: import_prop_types.default.elementType,
  /**
   * Adjust the duration of the content expand transition.
   * Passed as a prop to the transition component.
   *
   * Set to 'auto' to automatically calculate transition time based on height.
   * @default 'auto'
   */
  transitionDuration: import_prop_types.default.oneOfType([import_prop_types.default.oneOf(["auto"]), import_prop_types.default.number, import_prop_types.default.shape({
    appear: import_prop_types.default.number,
    enter: import_prop_types.default.number,
    exit: import_prop_types.default.number
  })]),
  /**
   * Props applied to the transition element.
   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.
   */
  TransitionProps: import_prop_types.default.object
} : void 0;
var StepContent_default = StepContent;

export {
  getStepContentUtilityClass,
  stepContentClasses_default,
  StepContent_default
};
//# sourceMappingURL=chunk-VYG6XW6H.js.map
