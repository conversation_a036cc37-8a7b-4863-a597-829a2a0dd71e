{"version": 3, "sources": ["../../@mui/x-date-pickers/internals/hooks/useDesktopPicker/useDesktopPicker.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"props\", \"getOpenDialogAriaText\"],\n  _excluded2 = [\"ownerState\"],\n  _excluded3 = [\"ownerState\"];\nimport * as React from 'react';\nimport { useSlotProps } from '@mui/base/utils';\nimport MuiInputAdornment from '@mui/material/InputAdornment';\nimport IconButton from '@mui/material/IconButton';\nimport useForkRef from '@mui/utils/useForkRef';\nimport useId from '@mui/utils/useId';\nimport { PickersPopper } from '../../components/PickersPopper';\nimport { useUtils } from '../useUtils';\nimport { usePicker } from '../usePicker';\nimport { LocalizationProvider } from '../../../LocalizationProvider';\nimport { PickersLayout } from '../../../PickersLayout';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * Hook managing all the single-date desktop pickers:\n * - DesktopDatePicker\n * - DesktopDateTimePicker\n * - DesktopTimePicker\n */\nexport const useDesktopPicker = _ref => {\n  var _innerSlotProps$toolb, _innerSlotProps$toolb2, _slots$inputAdornment, _slots$openPickerButt, _slots$layout;\n  let {\n      props,\n      getOpenDialogAriaText\n    } = _ref,\n    pickerParams = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const {\n    slots,\n    slotProps: innerSlotProps,\n    className,\n    sx,\n    format,\n    formatDensity,\n    timezone,\n    name,\n    label,\n    inputRef,\n    readOnly,\n    disabled,\n    autoFocus,\n    localeText,\n    reduceAnimations\n  } = props;\n  const utils = useUtils();\n  const internalInputRef = React.useRef(null);\n  const containerRef = React.useRef(null);\n  const labelId = useId();\n  const isToolbarHidden = (_innerSlotProps$toolb = innerSlotProps == null || (_innerSlotProps$toolb2 = innerSlotProps.toolbar) == null ? void 0 : _innerSlotProps$toolb2.hidden) != null ? _innerSlotProps$toolb : false;\n  const {\n    open,\n    actions,\n    hasUIView,\n    layoutProps,\n    renderCurrentView,\n    shouldRestoreFocus,\n    fieldProps: pickerFieldProps\n  } = usePicker(_extends({}, pickerParams, {\n    props,\n    inputRef: internalInputRef,\n    autoFocusView: true,\n    additionalViewProps: {},\n    wrapperVariant: 'desktop'\n  }));\n  const InputAdornment = (_slots$inputAdornment = slots.inputAdornment) != null ? _slots$inputAdornment : MuiInputAdornment;\n  const _useSlotProps = useSlotProps({\n      elementType: InputAdornment,\n      externalSlotProps: innerSlotProps == null ? void 0 : innerSlotProps.inputAdornment,\n      additionalProps: {\n        position: 'end'\n      },\n      ownerState: props\n    }),\n    inputAdornmentProps = _objectWithoutPropertiesLoose(_useSlotProps, _excluded2);\n  const OpenPickerButton = (_slots$openPickerButt = slots.openPickerButton) != null ? _slots$openPickerButt : IconButton;\n  const _useSlotProps2 = useSlotProps({\n      elementType: OpenPickerButton,\n      externalSlotProps: innerSlotProps == null ? void 0 : innerSlotProps.openPickerButton,\n      additionalProps: {\n        disabled: disabled || readOnly,\n        onClick: open ? actions.onClose : actions.onOpen,\n        'aria-label': getOpenDialogAriaText(pickerFieldProps.value, utils),\n        edge: inputAdornmentProps.position\n      },\n      ownerState: props\n    }),\n    openPickerButtonProps = _objectWithoutPropertiesLoose(_useSlotProps2, _excluded3);\n  const OpenPickerIcon = slots.openPickerIcon;\n  const Field = slots.field;\n  const fieldProps = useSlotProps({\n    elementType: Field,\n    externalSlotProps: innerSlotProps == null ? void 0 : innerSlotProps.field,\n    additionalProps: _extends({}, pickerFieldProps, isToolbarHidden && {\n      id: labelId\n    }, {\n      readOnly,\n      disabled,\n      className,\n      sx,\n      format,\n      formatDensity,\n      timezone,\n      label,\n      name,\n      autoFocus: autoFocus && !props.open,\n      focused: open ? true : undefined\n    }),\n    ownerState: props\n  });\n\n  // TODO: Move to `useSlotProps` when https://github.com/mui/material-ui/pull/35088 will be merged\n  if (hasUIView) {\n    fieldProps.InputProps = _extends({}, fieldProps.InputProps, {\n      ref: containerRef,\n      [`${inputAdornmentProps.position}Adornment`]: /*#__PURE__*/_jsx(InputAdornment, _extends({}, inputAdornmentProps, {\n        children: /*#__PURE__*/_jsx(OpenPickerButton, _extends({}, openPickerButtonProps, {\n          children: /*#__PURE__*/_jsx(OpenPickerIcon, _extends({}, innerSlotProps == null ? void 0 : innerSlotProps.openPickerIcon))\n        }))\n      }))\n    });\n  }\n  const slotsForField = _extends({\n    textField: slots.textField,\n    clearIcon: slots.clearIcon,\n    clearButton: slots.clearButton\n  }, fieldProps.slots);\n  const Layout = (_slots$layout = slots.layout) != null ? _slots$layout : PickersLayout;\n  const handleInputRef = useForkRef(internalInputRef, fieldProps.inputRef, inputRef);\n  let labelledById = labelId;\n  if (isToolbarHidden) {\n    if (label) {\n      labelledById = `${labelId}-label`;\n    } else {\n      labelledById = undefined;\n    }\n  }\n  const slotProps = _extends({}, innerSlotProps, {\n    toolbar: _extends({}, innerSlotProps == null ? void 0 : innerSlotProps.toolbar, {\n      titleId: labelId\n    }),\n    popper: _extends({\n      'aria-labelledby': labelledById\n    }, innerSlotProps == null ? void 0 : innerSlotProps.popper)\n  });\n  const renderPicker = () => /*#__PURE__*/_jsxs(LocalizationProvider, {\n    localeText: localeText,\n    children: [/*#__PURE__*/_jsx(Field, _extends({}, fieldProps, {\n      slots: slotsForField,\n      slotProps: slotProps,\n      inputRef: handleInputRef\n    })), /*#__PURE__*/_jsx(PickersPopper, _extends({\n      role: \"dialog\",\n      placement: \"bottom-start\",\n      anchorEl: containerRef.current\n    }, actions, {\n      open: open,\n      slots: slots,\n      slotProps: slotProps,\n      shouldRestoreFocus: shouldRestoreFocus,\n      reduceAnimations: reduceAnimations,\n      children: /*#__PURE__*/_jsx(Layout, _extends({}, layoutProps, slotProps == null ? void 0 : slotProps.layout, {\n        slots: slots,\n        slotProps: slotProps,\n        children: renderCurrentView()\n      }))\n    }))]\n  });\n  return {\n    renderPicker\n  };\n};"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAKA,YAAuB;AAWvB,yBAA4B;AAC5B,IAAAA,sBAA8B;AAf9B,IAAM,YAAY,CAAC,SAAS,uBAAuB;AAAnD,IACE,aAAa,CAAC,YAAY;AAD5B,IAEE,aAAa,CAAC,YAAY;AAoBrB,IAAM,mBAAmB,UAAQ;AACtC,MAAI,uBAAuB,wBAAwB,uBAAuB,uBAAuB;AACjG,MAAI;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,MACJ,eAAe,8BAA8B,MAAM,SAAS;AAC9D,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ,SAAS;AACvB,QAAM,mBAAyB,aAAO,IAAI;AAC1C,QAAM,eAAqB,aAAO,IAAI;AACtC,QAAM,UAAU,MAAM;AACtB,QAAM,mBAAmB,wBAAwB,kBAAkB,SAAS,yBAAyB,eAAe,YAAY,OAAO,SAAS,uBAAuB,WAAW,OAAO,wBAAwB;AACjN,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,EACd,IAAI,UAAU,SAAS,CAAC,GAAG,cAAc;AAAA,IACvC;AAAA,IACA,UAAU;AAAA,IACV,eAAe;AAAA,IACf,qBAAqB,CAAC;AAAA,IACtB,gBAAgB;AAAA,EAClB,CAAC,CAAC;AACF,QAAM,kBAAkB,wBAAwB,MAAM,mBAAmB,OAAO,wBAAwB;AACxG,QAAM,gBAAgB,aAAa;AAAA,IAC/B,aAAa;AAAA,IACb,mBAAmB,kBAAkB,OAAO,SAAS,eAAe;AAAA,IACpE,iBAAiB;AAAA,MACf,UAAU;AAAA,IACZ;AAAA,IACA,YAAY;AAAA,EACd,CAAC,GACD,sBAAsB,8BAA8B,eAAe,UAAU;AAC/E,QAAM,oBAAoB,wBAAwB,MAAM,qBAAqB,OAAO,wBAAwB;AAC5G,QAAM,iBAAiB,aAAa;AAAA,IAChC,aAAa;AAAA,IACb,mBAAmB,kBAAkB,OAAO,SAAS,eAAe;AAAA,IACpE,iBAAiB;AAAA,MACf,UAAU,YAAY;AAAA,MACtB,SAAS,OAAO,QAAQ,UAAU,QAAQ;AAAA,MAC1C,cAAc,sBAAsB,iBAAiB,OAAO,KAAK;AAAA,MACjE,MAAM,oBAAoB;AAAA,IAC5B;AAAA,IACA,YAAY;AAAA,EACd,CAAC,GACD,wBAAwB,8BAA8B,gBAAgB,UAAU;AAClF,QAAM,iBAAiB,MAAM;AAC7B,QAAM,QAAQ,MAAM;AACpB,QAAM,aAAa,aAAa;AAAA,IAC9B,aAAa;AAAA,IACb,mBAAmB,kBAAkB,OAAO,SAAS,eAAe;AAAA,IACpE,iBAAiB,SAAS,CAAC,GAAG,kBAAkB,mBAAmB;AAAA,MACjE,IAAI;AAAA,IACN,GAAG;AAAA,MACD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW,aAAa,CAAC,MAAM;AAAA,MAC/B,SAAS,OAAO,OAAO;AAAA,IACzB,CAAC;AAAA,IACD,YAAY;AAAA,EACd,CAAC;AAGD,MAAI,WAAW;AACb,eAAW,aAAa,SAAS,CAAC,GAAG,WAAW,YAAY;AAAA,MAC1D,KAAK;AAAA,MACL,CAAC,GAAG,oBAAoB,QAAQ,WAAW,OAAgB,mBAAAC,KAAK,gBAAgB,SAAS,CAAC,GAAG,qBAAqB;AAAA,QAChH,cAAuB,mBAAAA,KAAK,kBAAkB,SAAS,CAAC,GAAG,uBAAuB;AAAA,UAChF,cAAuB,mBAAAA,KAAK,gBAAgB,SAAS,CAAC,GAAG,kBAAkB,OAAO,SAAS,eAAe,cAAc,CAAC;AAAA,QAC3H,CAAC,CAAC;AAAA,MACJ,CAAC,CAAC;AAAA,IACJ,CAAC;AAAA,EACH;AACA,QAAM,gBAAgB,SAAS;AAAA,IAC7B,WAAW,MAAM;AAAA,IACjB,WAAW,MAAM;AAAA,IACjB,aAAa,MAAM;AAAA,EACrB,GAAG,WAAW,KAAK;AACnB,QAAM,UAAU,gBAAgB,MAAM,WAAW,OAAO,gBAAgB;AACxE,QAAM,iBAAiB,WAAW,kBAAkB,WAAW,UAAU,QAAQ;AACjF,MAAI,eAAe;AACnB,MAAI,iBAAiB;AACnB,QAAI,OAAO;AACT,qBAAe,GAAG,OAAO;AAAA,IAC3B,OAAO;AACL,qBAAe;AAAA,IACjB;AAAA,EACF;AACA,QAAM,YAAY,SAAS,CAAC,GAAG,gBAAgB;AAAA,IAC7C,SAAS,SAAS,CAAC,GAAG,kBAAkB,OAAO,SAAS,eAAe,SAAS;AAAA,MAC9E,SAAS;AAAA,IACX,CAAC;AAAA,IACD,QAAQ,SAAS;AAAA,MACf,mBAAmB;AAAA,IACrB,GAAG,kBAAkB,OAAO,SAAS,eAAe,MAAM;AAAA,EAC5D,CAAC;AACD,QAAM,eAAe,UAAmB,oBAAAC,MAAM,sBAAsB;AAAA,IAClE;AAAA,IACA,UAAU,KAAc,mBAAAD,KAAK,OAAO,SAAS,CAAC,GAAG,YAAY;AAAA,MAC3D,OAAO;AAAA,MACP;AAAA,MACA,UAAU;AAAA,IACZ,CAAC,CAAC,OAAgB,mBAAAA,KAAK,eAAe,SAAS;AAAA,MAC7C,MAAM;AAAA,MACN,WAAW;AAAA,MACX,UAAU,aAAa;AAAA,IACzB,GAAG,SAAS;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAuB,mBAAAA,KAAK,QAAQ,SAAS,CAAC,GAAG,aAAa,aAAa,OAAO,SAAS,UAAU,QAAQ;AAAA,QAC3G;AAAA,QACA;AAAA,QACA,UAAU,kBAAkB;AAAA,MAC9B,CAAC,CAAC;AAAA,IACJ,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACD,SAAO;AAAA,IACL;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "_jsx", "_jsxs"]}