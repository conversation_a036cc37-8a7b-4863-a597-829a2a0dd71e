{"version": 3, "sources": ["../../@mui/lab/TimelineContent/TimelineContent.js", "../../@mui/lab/TimelineContent/timelineContentClasses.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport Typography from '@mui/material/Typography';\nimport TimelineContext from '../Timeline/TimelineContext';\nimport { getTimelineContentUtilityClass } from './timelineContentClasses';\nimport convertTimelinePositionToClass from '../internal/convertTimelinePositionToClass';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    position,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', convertTimelinePositionToClass(position)]\n  };\n  return composeClasses(slots, getTimelineContentUtilityClass, classes);\n};\nconst TimelineContentRoot = styled(Typography, {\n  name: 'MuiTimelineContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[convertTimelinePositionToClass(ownerState.position)]];\n  }\n})(({\n  ownerState\n}) => _extends({\n  flex: 1,\n  padding: '6px 16px',\n  textAlign: 'left'\n}, ownerState.position === 'left' && {\n  textAlign: 'right'\n}));\nconst TimelineContent = /*#__PURE__*/React.forwardRef(function TimelineContent(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimelineContent'\n  });\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    position: positionContext\n  } = React.useContext(TimelineContext);\n  const ownerState = _extends({}, props, {\n    position: positionContext || 'right'\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TimelineContentRoot, _extends({\n    component: \"div\",\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? TimelineContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TimelineContent;", "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTimelineContentUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineContent', slot);\n}\nconst timelineContentClasses = generateUtilityClasses('MuiTimelineContent', ['root', 'positionLeft', 'positionRight', 'positionAlternate', 'positionAlternateReverse']);\nexport default timelineContentClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AAEA,YAAuB;AACvB,wBAAsB;;;ACJf,SAAS,+BAA+B,MAAM;AACnD,SAAO,qBAAqB,sBAAsB,IAAI;AACxD;AACA,IAAM,yBAAyB,uBAAuB,sBAAsB,CAAC,QAAQ,gBAAgB,iBAAiB,qBAAqB,0BAA0B,CAAC;AACtK,IAAO,iCAAQ;;;ADQf,yBAA4B;AAV5B,IAAM,YAAY,CAAC,WAAW;AAW9B,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,+BAA+B,QAAQ,CAAC;AAAA,EACzD;AACA,SAAO,eAAe,OAAO,gCAAgC,OAAO;AACtE;AACA,IAAM,sBAAsB,eAAO,oBAAY;AAAA,EAC7C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,OAAO,+BAA+B,WAAW,QAAQ,CAAC,CAAC;AAAA,EAClF;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AACF,MAAM,SAAS;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AACb,GAAG,WAAW,aAAa,UAAU;AAAA,EACnC,WAAW;AACb,CAAC,CAAC;AACF,IAAM,kBAAqC,iBAAW,SAASA,iBAAgB,SAAS,KAAK;AAC3F,QAAM,QAAQ,cAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM;AAAA,IACJ,UAAU;AAAA,EACZ,IAAU,iBAAW,uBAAe;AACpC,QAAM,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,IACrC,UAAU,mBAAmB;AAAA,EAC/B,CAAC;AACD,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,qBAAqB,SAAS;AAAA,IACrD,WAAW;AAAA,IACX,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,IACA;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,OAAwC,gBAAgB,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQzF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,0BAAQ;", "names": ["TimelineContent", "_jsx", "PropTypes"]}