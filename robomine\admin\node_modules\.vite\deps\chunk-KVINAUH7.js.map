{"version": 3, "sources": ["../../@mui/x-date-pickers/internals/hooks/useMobilePicker/useMobilePicker.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"props\", \"getOpenDialogAriaText\"];\nimport * as React from 'react';\nimport { useSlotProps } from '@mui/base/utils';\nimport useForkRef from '@mui/utils/useForkRef';\nimport useId from '@mui/utils/useId';\nimport { PickersModalDialog } from '../../components/PickersModalDialog';\nimport { usePicker } from '../usePicker';\nimport { onSpaceOrEnter } from '../../utils/utils';\nimport { useUtils } from '../useUtils';\nimport { LocalizationProvider } from '../../../LocalizationProvider';\nimport { PickersLayout } from '../../../PickersLayout';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * Hook managing all the single-date mobile pickers:\n * - MobileDatePicker\n * - MobileDateTimePicker\n * - MobileTimePicker\n */\nexport const useMobilePicker = _ref => {\n  var _innerSlotProps$toolb, _innerSlotProps$toolb2, _slots$layout;\n  let {\n      props,\n      getOpenDialogAriaText\n    } = _ref,\n    pickerParams = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const {\n    slots,\n    slotProps: innerSlotProps,\n    className,\n    sx,\n    format,\n    formatDensity,\n    timezone,\n    name,\n    label,\n    inputRef,\n    readOnly,\n    disabled,\n    localeText\n  } = props;\n  const utils = useUtils();\n  const internalInputRef = React.useRef(null);\n  const labelId = useId();\n  const isToolbarHidden = (_innerSlotProps$toolb = innerSlotProps == null || (_innerSlotProps$toolb2 = innerSlotProps.toolbar) == null ? void 0 : _innerSlotProps$toolb2.hidden) != null ? _innerSlotProps$toolb : false;\n  const {\n    open,\n    actions,\n    layoutProps,\n    renderCurrentView,\n    fieldProps: pickerFieldProps\n  } = usePicker(_extends({}, pickerParams, {\n    props,\n    inputRef: internalInputRef,\n    autoFocusView: true,\n    additionalViewProps: {},\n    wrapperVariant: 'mobile'\n  }));\n  const Field = slots.field;\n  const fieldProps = useSlotProps({\n    elementType: Field,\n    externalSlotProps: innerSlotProps == null ? void 0 : innerSlotProps.field,\n    additionalProps: _extends({}, pickerFieldProps, isToolbarHidden && {\n      id: labelId\n    }, !(disabled || readOnly) && {\n      onClick: actions.onOpen,\n      onKeyDown: onSpaceOrEnter(actions.onOpen)\n    }, {\n      readOnly: readOnly != null ? readOnly : true,\n      disabled,\n      className,\n      sx,\n      format,\n      formatDensity,\n      timezone,\n      label,\n      name\n    }),\n    ownerState: props\n  });\n\n  // TODO: Move to `useSlotProps` when https://github.com/mui/material-ui/pull/35088 will be merged\n  fieldProps.inputProps = _extends({}, fieldProps.inputProps, {\n    'aria-label': getOpenDialogAriaText(pickerFieldProps.value, utils)\n  });\n  const slotsForField = _extends({\n    textField: slots.textField\n  }, fieldProps.slots);\n  const Layout = (_slots$layout = slots.layout) != null ? _slots$layout : PickersLayout;\n  const handleInputRef = useForkRef(internalInputRef, fieldProps.inputRef, inputRef);\n  let labelledById = labelId;\n  if (isToolbarHidden) {\n    if (label) {\n      labelledById = `${labelId}-label`;\n    } else {\n      labelledById = undefined;\n    }\n  }\n  const slotProps = _extends({}, innerSlotProps, {\n    toolbar: _extends({}, innerSlotProps == null ? void 0 : innerSlotProps.toolbar, {\n      titleId: labelId\n    }),\n    mobilePaper: _extends({\n      'aria-labelledby': labelledById\n    }, innerSlotProps == null ? void 0 : innerSlotProps.mobilePaper)\n  });\n  const renderPicker = () => /*#__PURE__*/_jsxs(LocalizationProvider, {\n    localeText: localeText,\n    children: [/*#__PURE__*/_jsx(Field, _extends({}, fieldProps, {\n      slots: slotsForField,\n      slotProps: slotProps,\n      inputRef: handleInputRef\n    })), /*#__PURE__*/_jsx(PickersModalDialog, _extends({}, actions, {\n      open: open,\n      slots: slots,\n      slotProps: slotProps,\n      children: /*#__PURE__*/_jsx(Layout, _extends({}, layoutProps, slotProps == null ? void 0 : slotProps.layout, {\n        slots: slots,\n        slotProps: slotProps,\n        children: renderCurrentView()\n      }))\n    }))]\n  });\n  return {\n    renderPicker\n  };\n};"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAGA,YAAuB;AAUvB,yBAA4B;AAC5B,IAAAA,sBAA8B;AAZ9B,IAAM,YAAY,CAAC,SAAS,uBAAuB;AAmB5C,IAAM,kBAAkB,UAAQ;AACrC,MAAI,uBAAuB,wBAAwB;AACnD,MAAI;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,MACJ,eAAe,8BAA8B,MAAM,SAAS;AAC9D,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ,SAAS;AACvB,QAAM,mBAAyB,aAAO,IAAI;AAC1C,QAAM,UAAU,MAAM;AACtB,QAAM,mBAAmB,wBAAwB,kBAAkB,SAAS,yBAAyB,eAAe,YAAY,OAAO,SAAS,uBAAuB,WAAW,OAAO,wBAAwB;AACjN,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,EACd,IAAI,UAAU,SAAS,CAAC,GAAG,cAAc;AAAA,IACvC;AAAA,IACA,UAAU;AAAA,IACV,eAAe;AAAA,IACf,qBAAqB,CAAC;AAAA,IACtB,gBAAgB;AAAA,EAClB,CAAC,CAAC;AACF,QAAM,QAAQ,MAAM;AACpB,QAAM,aAAa,aAAa;AAAA,IAC9B,aAAa;AAAA,IACb,mBAAmB,kBAAkB,OAAO,SAAS,eAAe;AAAA,IACpE,iBAAiB,SAAS,CAAC,GAAG,kBAAkB,mBAAmB;AAAA,MACjE,IAAI;AAAA,IACN,GAAG,EAAE,YAAY,aAAa;AAAA,MAC5B,SAAS,QAAQ;AAAA,MACjB,WAAW,eAAe,QAAQ,MAAM;AAAA,IAC1C,GAAG;AAAA,MACD,UAAU,YAAY,OAAO,WAAW;AAAA,MACxC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IACD,YAAY;AAAA,EACd,CAAC;AAGD,aAAW,aAAa,SAAS,CAAC,GAAG,WAAW,YAAY;AAAA,IAC1D,cAAc,sBAAsB,iBAAiB,OAAO,KAAK;AAAA,EACnE,CAAC;AACD,QAAM,gBAAgB,SAAS;AAAA,IAC7B,WAAW,MAAM;AAAA,EACnB,GAAG,WAAW,KAAK;AACnB,QAAM,UAAU,gBAAgB,MAAM,WAAW,OAAO,gBAAgB;AACxE,QAAM,iBAAiB,WAAW,kBAAkB,WAAW,UAAU,QAAQ;AACjF,MAAI,eAAe;AACnB,MAAI,iBAAiB;AACnB,QAAI,OAAO;AACT,qBAAe,GAAG,OAAO;AAAA,IAC3B,OAAO;AACL,qBAAe;AAAA,IACjB;AAAA,EACF;AACA,QAAM,YAAY,SAAS,CAAC,GAAG,gBAAgB;AAAA,IAC7C,SAAS,SAAS,CAAC,GAAG,kBAAkB,OAAO,SAAS,eAAe,SAAS;AAAA,MAC9E,SAAS;AAAA,IACX,CAAC;AAAA,IACD,aAAa,SAAS;AAAA,MACpB,mBAAmB;AAAA,IACrB,GAAG,kBAAkB,OAAO,SAAS,eAAe,WAAW;AAAA,EACjE,CAAC;AACD,QAAM,eAAe,UAAmB,oBAAAC,MAAM,sBAAsB;AAAA,IAClE;AAAA,IACA,UAAU,KAAc,mBAAAC,KAAK,OAAO,SAAS,CAAC,GAAG,YAAY;AAAA,MAC3D,OAAO;AAAA,MACP;AAAA,MACA,UAAU;AAAA,IACZ,CAAC,CAAC,OAAgB,mBAAAA,KAAK,oBAAoB,SAAS,CAAC,GAAG,SAAS;AAAA,MAC/D;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAuB,mBAAAA,KAAK,QAAQ,SAAS,CAAC,GAAG,aAAa,aAAa,OAAO,SAAS,UAAU,QAAQ;AAAA,QAC3G;AAAA,QACA;AAAA,QACA,UAAU,kBAAkB;AAAA,MAC9B,CAAC,CAAC;AAAA,IACJ,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACD,SAAO;AAAA,IACL;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "_jsxs", "_jsx"]}