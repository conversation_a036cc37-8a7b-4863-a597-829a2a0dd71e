{"version": 3, "sources": ["../../@mui/material/Zoom/Zoom.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"addEndListener\", \"appear\", \"children\", \"easing\", \"in\", \"onEnter\", \"onEntered\", \"onEntering\", \"onExit\", \"onExited\", \"onExiting\", \"style\", \"timeout\", \"TransitionComponent\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { Transition } from 'react-transition-group';\nimport elementAcceptingRef from '@mui/utils/elementAcceptingRef';\nimport useTheme from '../styles/useTheme';\nimport { reflow, getTransitionProps } from '../transitions/utils';\nimport useForkRef from '../utils/useForkRef';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst styles = {\n  entering: {\n    transform: 'none'\n  },\n  entered: {\n    transform: 'none'\n  }\n};\n\n/**\n * The Zoom transition can be used for the floating variant of the\n * [Button](/material-ui/react-button/#floating-action-buttons) component.\n * It uses [react-transition-group](https://github.com/reactjs/react-transition-group) internally.\n */\nconst Zoom = /*#__PURE__*/React.forwardRef(function Zoom(props, ref) {\n  const theme = useTheme();\n  const defaultTimeout = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n      addEndListener,\n      appear = true,\n      children,\n      easing,\n      in: inProp,\n      onEnter,\n      onEntered,\n      onEntering,\n      onExit,\n      onExited,\n      onExiting,\n      style,\n      timeout = defaultTimeout,\n      // eslint-disable-next-line react/prop-types\n      TransitionComponent = Transition\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const nodeRef = React.useRef(null);\n  const handleRef = useForkRef(nodeRef, children.ref, ref);\n  const normalizedTransitionCallback = callback => maybeIsAppearing => {\n    if (callback) {\n      const node = nodeRef.current;\n\n      // onEnterXxx and onExitXxx callbacks have a different arguments.length value.\n      if (maybeIsAppearing === undefined) {\n        callback(node);\n      } else {\n        callback(node, maybeIsAppearing);\n      }\n    }\n  };\n  const handleEntering = normalizedTransitionCallback(onEntering);\n  const handleEnter = normalizedTransitionCallback((node, isAppearing) => {\n    reflow(node); // So the animation always start from the start.\n\n    const transitionProps = getTransitionProps({\n      style,\n      timeout,\n      easing\n    }, {\n      mode: 'enter'\n    });\n    node.style.webkitTransition = theme.transitions.create('transform', transitionProps);\n    node.style.transition = theme.transitions.create('transform', transitionProps);\n    if (onEnter) {\n      onEnter(node, isAppearing);\n    }\n  });\n  const handleEntered = normalizedTransitionCallback(onEntered);\n  const handleExiting = normalizedTransitionCallback(onExiting);\n  const handleExit = normalizedTransitionCallback(node => {\n    const transitionProps = getTransitionProps({\n      style,\n      timeout,\n      easing\n    }, {\n      mode: 'exit'\n    });\n    node.style.webkitTransition = theme.transitions.create('transform', transitionProps);\n    node.style.transition = theme.transitions.create('transform', transitionProps);\n    if (onExit) {\n      onExit(node);\n    }\n  });\n  const handleExited = normalizedTransitionCallback(onExited);\n  const handleAddEndListener = next => {\n    if (addEndListener) {\n      // Old call signature before `react-transition-group` implemented `nodeRef`\n      addEndListener(nodeRef.current, next);\n    }\n  };\n  return /*#__PURE__*/_jsx(TransitionComponent, _extends({\n    appear: appear,\n    in: inProp,\n    nodeRef: nodeRef,\n    onEnter: handleEnter,\n    onEntered: handleEntered,\n    onEntering: handleEntering,\n    onExit: handleExit,\n    onExited: handleExited,\n    onExiting: handleExiting,\n    addEndListener: handleAddEndListener,\n    timeout: timeout\n  }, other, {\n    children: (state, childProps) => {\n      return /*#__PURE__*/React.cloneElement(children, _extends({\n        style: _extends({\n          transform: 'scale(0)',\n          visibility: state === 'exited' && !inProp ? 'hidden' : undefined\n        }, styles[state], style, children.props.style),\n        ref: handleRef\n      }, childProps));\n    }\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Zoom.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Add a custom transition end trigger. Called with the transitioning DOM\n   * node and a done callback. Allows for more fine grained transition end\n   * logic. Note: Timeouts are still used as a fallback if provided.\n   */\n  addEndListener: PropTypes.func,\n  /**\n   * Perform the enter transition when it first mounts if `in` is also `true`.\n   * Set this to `false` to disable this behavior.\n   * @default true\n   */\n  appear: PropTypes.bool,\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * The transition timing function.\n   * You may specify a single easing or a object containing enter and exit values.\n   */\n  easing: PropTypes.oneOfType([PropTypes.shape({\n    enter: PropTypes.string,\n    exit: PropTypes.string\n  }), PropTypes.string]),\n  /**\n   * If `true`, the component will transition in.\n   */\n  in: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  onEnter: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onEntered: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onEntering: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onExit: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onExited: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onExiting: PropTypes.func,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  timeout: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })])\n} : void 0;\nexport default Zoom;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AAGA,YAAuB;AACvB,wBAAsB;AAMtB,yBAA4B;AAR5B,IAAM,YAAY,CAAC,kBAAkB,UAAU,YAAY,UAAU,MAAM,WAAW,aAAa,cAAc,UAAU,YAAY,aAAa,SAAS,WAAW,qBAAqB;AAS7L,IAAM,SAAS;AAAA,EACb,UAAU;AAAA,IACR,WAAW;AAAA,EACb;AAAA,EACA,SAAS;AAAA,IACP,WAAW;AAAA,EACb;AACF;AAOA,IAAM,OAA0B,iBAAW,SAASA,MAAK,OAAO,KAAK;AACnE,QAAM,QAAQ,SAAS;AACvB,QAAM,iBAAiB;AAAA,IACrB,OAAO,MAAM,YAAY,SAAS;AAAA,IAClC,MAAM,MAAM,YAAY,SAAS;AAAA,EACnC;AACA,QAAM;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA,IAAI;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA;AAAA,IAEV,sBAAsB;AAAA,EACxB,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM,UAAgB,aAAO,IAAI;AACjC,QAAM,YAAY,mBAAW,SAAS,SAAS,KAAK,GAAG;AACvD,QAAM,+BAA+B,cAAY,sBAAoB;AACnE,QAAI,UAAU;AACZ,YAAM,OAAO,QAAQ;AAGrB,UAAI,qBAAqB,QAAW;AAClC,iBAAS,IAAI;AAAA,MACf,OAAO;AACL,iBAAS,MAAM,gBAAgB;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACA,QAAM,iBAAiB,6BAA6B,UAAU;AAC9D,QAAM,cAAc,6BAA6B,CAAC,MAAM,gBAAgB;AACtE,WAAO,IAAI;AAEX,UAAM,kBAAkB,mBAAmB;AAAA,MACzC;AAAA,MACA;AAAA,MACA;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AACD,SAAK,MAAM,mBAAmB,MAAM,YAAY,OAAO,aAAa,eAAe;AACnF,SAAK,MAAM,aAAa,MAAM,YAAY,OAAO,aAAa,eAAe;AAC7E,QAAI,SAAS;AACX,cAAQ,MAAM,WAAW;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,QAAM,gBAAgB,6BAA6B,SAAS;AAC5D,QAAM,gBAAgB,6BAA6B,SAAS;AAC5D,QAAM,aAAa,6BAA6B,UAAQ;AACtD,UAAM,kBAAkB,mBAAmB;AAAA,MACzC;AAAA,MACA;AAAA,MACA;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AACD,SAAK,MAAM,mBAAmB,MAAM,YAAY,OAAO,aAAa,eAAe;AACnF,SAAK,MAAM,aAAa,MAAM,YAAY,OAAO,aAAa,eAAe;AAC7E,QAAI,QAAQ;AACV,aAAO,IAAI;AAAA,IACb;AAAA,EACF,CAAC;AACD,QAAM,eAAe,6BAA6B,QAAQ;AAC1D,QAAM,uBAAuB,UAAQ;AACnC,QAAI,gBAAgB;AAElB,qBAAe,QAAQ,SAAS,IAAI;AAAA,IACtC;AAAA,EACF;AACA,aAAoB,mBAAAC,KAAK,qBAAqB,SAAS;AAAA,IACrD;AAAA,IACA,IAAI;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,IACT,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB;AAAA,EACF,GAAG,OAAO;AAAA,IACR,UAAU,CAAC,OAAO,eAAe;AAC/B,aAA0B,mBAAa,UAAU,SAAS;AAAA,QACxD,OAAO,SAAS;AAAA,UACd,WAAW;AAAA,UACX,YAAY,UAAU,YAAY,CAAC,SAAS,WAAW;AAAA,QACzD,GAAG,OAAO,KAAK,GAAG,OAAO,SAAS,MAAM,KAAK;AAAA,QAC7C,KAAK;AAAA,MACP,GAAG,UAAU,CAAC;AAAA,IAChB;AAAA,EACF,CAAC,CAAC;AACJ,CAAC;AACD,OAAwC,KAAK,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU9E,gBAAgB,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,UAAU,4BAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9B,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM;AAAA,IAC3C,OAAO,kBAAAA,QAAU;AAAA,IACjB,MAAM,kBAAAA,QAAU;AAAA,EAClB,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAIrB,IAAI,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAId,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,YAAY,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAItB,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASjB,SAAS,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM;AAAA,IAC9D,QAAQ,kBAAAA,QAAU;AAAA,IAClB,OAAO,kBAAAA,QAAU;AAAA,IACjB,MAAM,kBAAAA,QAAU;AAAA,EAClB,CAAC,CAAC,CAAC;AACL,IAAI;AACJ,IAAO,eAAQ;", "names": ["Zoom", "_jsx", "PropTypes"]}