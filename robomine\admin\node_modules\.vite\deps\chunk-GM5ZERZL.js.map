{"version": 3, "sources": ["../../@mui/material/CssBaseline/CssBaseline.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useThemeProps from '../styles/useThemeProps';\nimport GlobalStyles from '../GlobalStyles';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const html = (theme, enableColorScheme) => _extends({\n  WebkitFontSmoothing: 'antialiased',\n  // Antialiasing.\n  MozOsxFontSmoothing: 'grayscale',\n  // Antialiasing.\n  // Change from `box-sizing: content-box` so that `width`\n  // is not affected by `padding` or `border`.\n  boxSizing: 'border-box',\n  // Fix font resize problem in iOS\n  WebkitTextSizeAdjust: '100%'\n}, enableColorScheme && !theme.vars && {\n  colorScheme: theme.palette.mode\n});\nexport const body = theme => _extends({\n  color: (theme.vars || theme).palette.text.primary\n}, theme.typography.body1, {\n  backgroundColor: (theme.vars || theme).palette.background.default,\n  '@media print': {\n    // Save printer ink.\n    backgroundColor: (theme.vars || theme).palette.common.white\n  }\n});\nexport const styles = (theme, enableColorScheme = false) => {\n  var _theme$components;\n  const colorSchemeStyles = {};\n  if (enableColorScheme && theme.colorSchemes) {\n    Object.entries(theme.colorSchemes).forEach(([key, scheme]) => {\n      var _scheme$palette;\n      colorSchemeStyles[theme.getColorSchemeSelector(key).replace(/\\s*&/, '')] = {\n        colorScheme: (_scheme$palette = scheme.palette) == null ? void 0 : _scheme$palette.mode\n      };\n    });\n  }\n  let defaultStyles = _extends({\n    html: html(theme, enableColorScheme),\n    '*, *::before, *::after': {\n      boxSizing: 'inherit'\n    },\n    'strong, b': {\n      fontWeight: theme.typography.fontWeightBold\n    },\n    body: _extends({\n      margin: 0\n    }, body(theme), {\n      // Add support for document.body.requestFullScreen().\n      // Other elements, if background transparent, are not supported.\n      '&::backdrop': {\n        backgroundColor: (theme.vars || theme).palette.background.default\n      }\n    })\n  }, colorSchemeStyles);\n  const themeOverrides = (_theme$components = theme.components) == null || (_theme$components = _theme$components.MuiCssBaseline) == null ? void 0 : _theme$components.styleOverrides;\n  if (themeOverrides) {\n    defaultStyles = [defaultStyles, themeOverrides];\n  }\n  return defaultStyles;\n};\n\n/**\n * Kickstart an elegant, consistent, and simple baseline to build upon.\n */\nfunction CssBaseline(inProps) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiCssBaseline'\n  });\n  const {\n    children,\n    enableColorScheme = false\n  } = props;\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(GlobalStyles, {\n      styles: theme => styles(theme, enableColorScheme)\n    }), children]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? CssBaseline.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * You can wrap a node.\n   */\n  children: PropTypes.node,\n  /**\n   * Enable `color-scheme` CSS property to use `theme.palette.mode`.\n   * For more details, check out https://developer.mozilla.org/en-US/docs/Web/CSS/color-scheme\n   * For browser support, check out https://caniuse.com/?search=color-scheme\n   * @default false\n   */\n  enableColorScheme: PropTypes.bool\n} : void 0;\nexport default CssBaseline;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA,YAAuB;AACvB,wBAAsB;AAGtB,yBAA4B;AAC5B,IAAAA,sBAA8B;AACvB,IAAM,OAAO,CAAC,OAAO,sBAAsB,SAAS;AAAA,EACzD,qBAAqB;AAAA;AAAA,EAErB,qBAAqB;AAAA;AAAA;AAAA;AAAA,EAIrB,WAAW;AAAA;AAAA,EAEX,sBAAsB;AACxB,GAAG,qBAAqB,CAAC,MAAM,QAAQ;AAAA,EACrC,aAAa,MAAM,QAAQ;AAC7B,CAAC;AACM,IAAM,OAAO,WAAS,SAAS;AAAA,EACpC,QAAQ,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAC5C,GAAG,MAAM,WAAW,OAAO;AAAA,EACzB,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,WAAW;AAAA,EAC1D,gBAAgB;AAAA;AAAA,IAEd,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,EACxD;AACF,CAAC;AACM,IAAM,SAAS,CAAC,OAAO,oBAAoB,UAAU;AAC1D,MAAI;AACJ,QAAM,oBAAoB,CAAC;AAC3B,MAAI,qBAAqB,MAAM,cAAc;AAC3C,WAAO,QAAQ,MAAM,YAAY,EAAE,QAAQ,CAAC,CAAC,KAAK,MAAM,MAAM;AAC5D,UAAI;AACJ,wBAAkB,MAAM,uBAAuB,GAAG,EAAE,QAAQ,QAAQ,EAAE,CAAC,IAAI;AAAA,QACzE,cAAc,kBAAkB,OAAO,YAAY,OAAO,SAAS,gBAAgB;AAAA,MACrF;AAAA,IACF,CAAC;AAAA,EACH;AACA,MAAI,gBAAgB,SAAS;AAAA,IAC3B,MAAM,KAAK,OAAO,iBAAiB;AAAA,IACnC,0BAA0B;AAAA,MACxB,WAAW;AAAA,IACb;AAAA,IACA,aAAa;AAAA,MACX,YAAY,MAAM,WAAW;AAAA,IAC/B;AAAA,IACA,MAAM,SAAS;AAAA,MACb,QAAQ;AAAA,IACV,GAAG,KAAK,KAAK,GAAG;AAAA;AAAA;AAAA,MAGd,eAAe;AAAA,QACb,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,WAAW;AAAA,MAC5D;AAAA,IACF,CAAC;AAAA,EACH,GAAG,iBAAiB;AACpB,QAAM,kBAAkB,oBAAoB,MAAM,eAAe,SAAS,oBAAoB,kBAAkB,mBAAmB,OAAO,SAAS,kBAAkB;AACrK,MAAI,gBAAgB;AAClB,oBAAgB,CAAC,eAAe,cAAc;AAAA,EAChD;AACA,SAAO;AACT;AAKA,SAAS,YAAY,SAAS;AAC5B,QAAM,QAAQ,cAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA,oBAAoB;AAAA,EACtB,IAAI;AACJ,aAAoB,oBAAAC,MAAY,gBAAU;AAAA,IACxC,UAAU,KAAc,mBAAAC,KAAK,sBAAc;AAAA,MACzC,QAAQ,WAAS,OAAO,OAAO,iBAAiB;AAAA,IAClD,CAAC,GAAG,QAAQ;AAAA,EACd,CAAC;AACH;AACA,OAAwC,YAAY,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQrF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOpB,mBAAmB,kBAAAA,QAAU;AAC/B,IAAI;AACJ,IAAO,sBAAQ;", "names": ["import_jsx_runtime", "_jsxs", "_jsx", "PropTypes"]}