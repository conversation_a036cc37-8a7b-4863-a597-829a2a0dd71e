import {
  generateUtilityClass,
  generateUtilityClasses
} from "./chunk-EH52VBW6.js";

// node_modules/@mui/material/DialogTitle/dialogTitleClasses.js
function getDialogTitleUtilityClass(slot) {
  return generateUtilityClass("MuiDialogTitle", slot);
}
var dialogTitleClasses = generateUtilityClasses("MuiDialogTitle", ["root"]);
var dialogTitleClasses_default = dialogTitleClasses;

export {
  getDialogTitleUtilityClass,
  dialogTitleClasses_default
};
//# sourceMappingURL=chunk-FBNZD5YK.js.map
