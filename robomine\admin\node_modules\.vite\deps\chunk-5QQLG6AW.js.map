{"version": 3, "sources": ["../../@mui/material/Fab/Fab.js", "../../@mui/material/Fab/fabClasses.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"color\", \"component\", \"disabled\", \"disableFocusRipple\", \"focusVisibleClassName\", \"size\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport ButtonBase from '../ButtonBase';\nimport capitalize from '../utils/capitalize';\nimport useThemeProps from '../styles/useThemeProps';\nimport fabClasses, { getFabUtilityClass } from './fabClasses';\nimport styled, { rootShouldForwardProp } from '../styles/styled';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    color,\n    variant,\n    classes,\n    size\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, `size${capitalize(size)}`, color === 'inherit' ? 'colorInherit' : color]\n  };\n  const composedClasses = composeClasses(slots, getFabUtilityClass, classes);\n  return _extends({}, classes, composedClasses);\n};\nconst FabRoot = styled(ButtonBase, {\n  name: 'MuiFab',\n  slot: 'Root',\n  shouldForwardProp: prop => rootShouldForwardProp(prop) || prop === 'classes',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], styles[`size${capitalize(ownerState.size)}`], ownerState.color === 'inherit' && styles.colorInherit, styles[capitalize(ownerState.size)], styles[ownerState.color]];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  var _theme$palette$getCon, _theme$palette;\n  return _extends({}, theme.typography.button, {\n    minHeight: 36,\n    transition: theme.transitions.create(['background-color', 'box-shadow', 'border-color'], {\n      duration: theme.transitions.duration.short\n    }),\n    borderRadius: '50%',\n    padding: 0,\n    minWidth: 0,\n    width: 56,\n    height: 56,\n    zIndex: (theme.vars || theme).zIndex.fab,\n    boxShadow: (theme.vars || theme).shadows[6],\n    '&:active': {\n      boxShadow: (theme.vars || theme).shadows[12]\n    },\n    color: theme.vars ? theme.vars.palette.text.primary : (_theme$palette$getCon = (_theme$palette = theme.palette).getContrastText) == null ? void 0 : _theme$palette$getCon.call(_theme$palette, theme.palette.grey[300]),\n    backgroundColor: (theme.vars || theme).palette.grey[300],\n    '&:hover': {\n      backgroundColor: (theme.vars || theme).palette.grey.A100,\n      // Reset on touch devices, it doesn't add specificity\n      '@media (hover: none)': {\n        backgroundColor: (theme.vars || theme).palette.grey[300]\n      },\n      textDecoration: 'none'\n    },\n    [`&.${fabClasses.focusVisible}`]: {\n      boxShadow: (theme.vars || theme).shadows[6]\n    }\n  }, ownerState.size === 'small' && {\n    width: 40,\n    height: 40\n  }, ownerState.size === 'medium' && {\n    width: 48,\n    height: 48\n  }, ownerState.variant === 'extended' && {\n    borderRadius: 48 / 2,\n    padding: '0 16px',\n    width: 'auto',\n    minHeight: 'auto',\n    minWidth: 48,\n    height: 48\n  }, ownerState.variant === 'extended' && ownerState.size === 'small' && {\n    width: 'auto',\n    padding: '0 8px',\n    borderRadius: 34 / 2,\n    minWidth: 34,\n    height: 34\n  }, ownerState.variant === 'extended' && ownerState.size === 'medium' && {\n    width: 'auto',\n    padding: '0 16px',\n    borderRadius: 40 / 2,\n    minWidth: 40,\n    height: 40\n  }, ownerState.color === 'inherit' && {\n    color: 'inherit'\n  });\n}, ({\n  theme,\n  ownerState\n}) => _extends({}, ownerState.color !== 'inherit' && ownerState.color !== 'default' && (theme.vars || theme).palette[ownerState.color] != null && {\n  color: (theme.vars || theme).palette[ownerState.color].contrastText,\n  backgroundColor: (theme.vars || theme).palette[ownerState.color].main,\n  '&:hover': {\n    backgroundColor: (theme.vars || theme).palette[ownerState.color].dark,\n    // Reset on touch devices, it doesn't add specificity\n    '@media (hover: none)': {\n      backgroundColor: (theme.vars || theme).palette[ownerState.color].main\n    }\n  }\n}), ({\n  theme\n}) => ({\n  [`&.${fabClasses.disabled}`]: {\n    color: (theme.vars || theme).palette.action.disabled,\n    boxShadow: (theme.vars || theme).shadows[0],\n    backgroundColor: (theme.vars || theme).palette.action.disabledBackground\n  }\n}));\nconst Fab = /*#__PURE__*/React.forwardRef(function Fab(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiFab'\n  });\n  const {\n      children,\n      className,\n      color = 'default',\n      component = 'button',\n      disabled = false,\n      disableFocusRipple = false,\n      focusVisibleClassName,\n      size = 'large',\n      variant = 'circular'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    color,\n    component,\n    disabled,\n    disableFocusRipple,\n    size,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(FabRoot, _extends({\n    className: clsx(classes.root, className),\n    component: component,\n    disabled: disabled,\n    focusRipple: !disableFocusRipple,\n    focusVisibleClassName: clsx(classes.focusVisible, focusVisibleClassName),\n    ownerState: ownerState,\n    ref: ref\n  }, other, {\n    classes: classes,\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Fab.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'default'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'error', 'info', 'inherit', 'primary', 'secondary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the  keyboard focus ripple is disabled.\n   * @default false\n   */\n  disableFocusRipple: PropTypes.bool,\n  /**\n   * If `true`, the ripple effect is disabled.\n   */\n  disableRipple: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  focusVisibleClassName: PropTypes.string,\n  /**\n   * The URL to link to when the button is clicked.\n   * If defined, an `a` element will be used as the root node.\n   */\n  href: PropTypes.string,\n  /**\n   * The size of the component.\n   * `small` is equivalent to the dense button styling.\n   * @default 'large'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['small', 'medium', 'large']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'circular'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['circular', 'extended']), PropTypes.string])\n} : void 0;\nexport default Fab;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getFabUtilityClass(slot) {\n  return generateUtilityClass('MuiFab', slot);\n}\nconst fabClasses = generateUtilityClasses('MuiFab', ['root', 'primary', 'secondary', 'extended', 'circular', 'focusVisible', 'disabled', 'colorInherit', 'sizeSmall', 'sizeMedium', 'sizeLarge', 'info', 'error', 'warning', 'success']);\nexport default fabClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AAEA,YAAuB;AACvB,wBAAsB;;;ACJf,SAAS,mBAAmB,MAAM;AACvC,SAAO,qBAAqB,UAAU,IAAI;AAC5C;AACA,IAAM,aAAa,uBAAuB,UAAU,CAAC,QAAQ,WAAW,aAAa,YAAY,YAAY,gBAAgB,YAAY,gBAAgB,aAAa,cAAc,aAAa,QAAQ,SAAS,WAAW,SAAS,CAAC;AACvO,IAAO,qBAAQ;;;ADQf,yBAA4B;AAV5B,IAAM,YAAY,CAAC,YAAY,aAAa,SAAS,aAAa,YAAY,sBAAsB,yBAAyB,QAAQ,SAAS;AAW9I,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,SAAS,OAAO,mBAAW,IAAI,CAAC,IAAI,UAAU,YAAY,iBAAiB,KAAK;AAAA,EACjG;AACA,QAAM,kBAAkB,eAAe,OAAO,oBAAoB,OAAO;AACzE,SAAO,SAAS,CAAC,GAAG,SAAS,eAAe;AAC9C;AACA,IAAM,UAAU,eAAO,oBAAY;AAAA,EACjC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,UAAQ,8BAAsB,IAAI,KAAK,SAAS;AAAA,EACnE,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,OAAO,WAAW,OAAO,GAAG,OAAO,OAAO,mBAAW,WAAW,IAAI,CAAC,EAAE,GAAG,WAAW,UAAU,aAAa,OAAO,cAAc,OAAO,mBAAW,WAAW,IAAI,CAAC,GAAG,OAAO,WAAW,KAAK,CAAC;AAAA,EACrN;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AAAA,EACA;AACF,MAAM;AACJ,MAAI,uBAAuB;AAC3B,SAAO,SAAS,CAAC,GAAG,MAAM,WAAW,QAAQ;AAAA,IAC3C,WAAW;AAAA,IACX,YAAY,MAAM,YAAY,OAAO,CAAC,oBAAoB,cAAc,cAAc,GAAG;AAAA,MACvF,UAAU,MAAM,YAAY,SAAS;AAAA,IACvC,CAAC;AAAA,IACD,cAAc;AAAA,IACd,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS,MAAM,QAAQ,OAAO,OAAO;AAAA,IACrC,YAAY,MAAM,QAAQ,OAAO,QAAQ,CAAC;AAAA,IAC1C,YAAY;AAAA,MACV,YAAY,MAAM,QAAQ,OAAO,QAAQ,EAAE;AAAA,IAC7C;AAAA,IACA,OAAO,MAAM,OAAO,MAAM,KAAK,QAAQ,KAAK,WAAW,yBAAyB,iBAAiB,MAAM,SAAS,oBAAoB,OAAO,SAAS,sBAAsB,KAAK,gBAAgB,MAAM,QAAQ,KAAK,GAAG,CAAC;AAAA,IACtN,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,KAAK,GAAG;AAAA,IACvD,WAAW;AAAA,MACT,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA;AAAA,MAEpD,wBAAwB;AAAA,QACtB,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,KAAK,GAAG;AAAA,MACzD;AAAA,MACA,gBAAgB;AAAA,IAClB;AAAA,IACA,CAAC,KAAK,mBAAW,YAAY,EAAE,GAAG;AAAA,MAChC,YAAY,MAAM,QAAQ,OAAO,QAAQ,CAAC;AAAA,IAC5C;AAAA,EACF,GAAG,WAAW,SAAS,WAAW;AAAA,IAChC,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,GAAG,WAAW,SAAS,YAAY;AAAA,IACjC,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,GAAG,WAAW,YAAY,cAAc;AAAA,IACtC,cAAc,KAAK;AAAA,IACnB,SAAS;AAAA,IACT,OAAO;AAAA,IACP,WAAW;AAAA,IACX,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,GAAG,WAAW,YAAY,cAAc,WAAW,SAAS,WAAW;AAAA,IACrE,OAAO;AAAA,IACP,SAAS;AAAA,IACT,cAAc,KAAK;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,GAAG,WAAW,YAAY,cAAc,WAAW,SAAS,YAAY;AAAA,IACtE,OAAO;AAAA,IACP,SAAS;AAAA,IACT,cAAc,KAAK;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,GAAG,WAAW,UAAU,aAAa;AAAA,IACnC,OAAO;AAAA,EACT,CAAC;AACH,GAAG,CAAC;AAAA,EACF;AAAA,EACA;AACF,MAAM,SAAS,CAAC,GAAG,WAAW,UAAU,aAAa,WAAW,UAAU,cAAc,MAAM,QAAQ,OAAO,QAAQ,WAAW,KAAK,KAAK,QAAQ;AAAA,EAChJ,QAAQ,MAAM,QAAQ,OAAO,QAAQ,WAAW,KAAK,EAAE;AAAA,EACvD,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,WAAW,KAAK,EAAE;AAAA,EACjE,WAAW;AAAA,IACT,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,WAAW,KAAK,EAAE;AAAA;AAAA,IAEjE,wBAAwB;AAAA,MACtB,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,WAAW,KAAK,EAAE;AAAA,IACnE;AAAA,EACF;AACF,CAAC,GAAG,CAAC;AAAA,EACH;AACF,OAAO;AAAA,EACL,CAAC,KAAK,mBAAW,QAAQ,EAAE,GAAG;AAAA,IAC5B,QAAQ,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,IAC5C,YAAY,MAAM,QAAQ,OAAO,QAAQ,CAAC;AAAA,IAC1C,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,EACxD;AACF,EAAE;AACF,IAAM,MAAyB,iBAAW,SAASA,KAAI,SAAS,KAAK;AACnE,QAAM,QAAQ,cAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,qBAAqB;AAAA,IACrB;AAAA,IACA,OAAO;AAAA,IACP,UAAU;AAAA,EACZ,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,SAAS,SAAS;AAAA,IACzC,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,IACA;AAAA,IACA,aAAa,CAAC;AAAA,IACd,uBAAuB,aAAK,QAAQ,cAAc,qBAAqB;AAAA,IACvE;AAAA,IACA;AAAA,EACF,GAAG,OAAO;AAAA,IACR;AAAA,IACA;AAAA,EACF,CAAC,CAAC;AACJ,CAAC;AACD,OAAwC,IAAI,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ7E,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOrB,OAAO,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,WAAW,SAAS,QAAQ,WAAW,WAAW,aAAa,WAAW,SAAS,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3L,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,oBAAoB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI9B,eAAe,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIzB,uBAAuB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjC,MAAM,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhB,MAAM,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,SAAS,UAAU,OAAO,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAIjI,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtJ,SAAS,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,YAAY,UAAU,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAClI,IAAI;AACJ,IAAO,cAAQ;", "names": ["Fab", "_jsx", "PropTypes"]}