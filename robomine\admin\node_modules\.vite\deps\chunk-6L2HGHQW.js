import {
  Tooltip_default
} from "./chunk-VERRNO2O.js";
import {
  Fab_default
} from "./chunk-5QQLG6AW.js";
import {
  capitalize_default
} from "./chunk-QGOONFNA.js";
import {
  styled_default
} from "./chunk-KIJLS2TV.js";
import {
  useThemeProps
} from "./chunk-ZYUAWKJJ.js";
import {
  require_colorManipulator
} from "./chunk-J4FXFBWI.js";
import {
  clsx_default
} from "./chunk-YV3COZNF.js";
import {
  composeClasses,
  generateUtilityClass,
  generateUtilityClasses
} from "./chunk-EH52VBW6.js";
import {
  require_prop_types
} from "./chunk-MDE6ZET7.js";
import {
  _objectWithoutPropertiesLoose
} from "./chunk-OBSDRUBD.js";
import {
  require_jsx_runtime
} from "./chunk-D4DBS43D.js";
import {
  _extends,
  init_extends
} from "./chunk-4GAI7T4A.js";
import {
  require_react
} from "./chunk-R56R2YIZ.js";
import {
  __toESM
} from "./chunk-BYPFWIQ6.js";

// node_modules/@mui/material/SpeedDialAction/SpeedDialAction.js
init_extends();
var React = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
var import_colorManipulator = __toESM(require_colorManipulator());

// node_modules/@mui/material/SpeedDialAction/speedDialActionClasses.js
function getSpeedDialActionUtilityClass(slot) {
  return generateUtilityClass("MuiSpeedDialAction", slot);
}
var speedDialActionClasses = generateUtilityClasses("MuiSpeedDialAction", ["fab", "fabClosed", "staticTooltip", "staticTooltipClosed", "staticTooltipLabel", "tooltipPlacementLeft", "tooltipPlacementRight"]);
var speedDialActionClasses_default = speedDialActionClasses;

// node_modules/@mui/material/SpeedDialAction/SpeedDialAction.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var _excluded = ["className", "delay", "FabProps", "icon", "id", "open", "TooltipClasses", "tooltipOpen", "tooltipPlacement", "tooltipTitle"];
var useUtilityClasses = (ownerState) => {
  const {
    open,
    tooltipPlacement,
    classes
  } = ownerState;
  const slots = {
    fab: ["fab", !open && "fabClosed"],
    staticTooltip: ["staticTooltip", `tooltipPlacement${capitalize_default(tooltipPlacement)}`, !open && "staticTooltipClosed"],
    staticTooltipLabel: ["staticTooltipLabel"]
  };
  return composeClasses(slots, getSpeedDialActionUtilityClass, classes);
};
var SpeedDialActionFab = styled_default(Fab_default, {
  name: "MuiSpeedDialAction",
  slot: "Fab",
  skipVariantsResolver: false,
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [styles.fab, !ownerState.open && styles.fabClosed];
  }
})(({
  theme,
  ownerState
}) => _extends({
  margin: 8,
  color: (theme.vars || theme).palette.text.secondary,
  backgroundColor: (theme.vars || theme).palette.background.paper,
  "&:hover": {
    backgroundColor: theme.vars ? theme.vars.palette.SpeedDialAction.fabHoverBg : (0, import_colorManipulator.emphasize)(theme.palette.background.paper, 0.15)
  },
  transition: `${theme.transitions.create("transform", {
    duration: theme.transitions.duration.shorter
  })}, opacity 0.8s`,
  opacity: 1
}, !ownerState.open && {
  opacity: 0,
  transform: "scale(0)"
}));
var SpeedDialActionStaticTooltip = styled_default("span", {
  name: "MuiSpeedDialAction",
  slot: "StaticTooltip",
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [styles.staticTooltip, !ownerState.open && styles.staticTooltipClosed, styles[`tooltipPlacement${capitalize_default(ownerState.tooltipPlacement)}`]];
  }
})(({
  theme,
  ownerState
}) => ({
  position: "relative",
  display: "flex",
  alignItems: "center",
  [`& .${speedDialActionClasses_default.staticTooltipLabel}`]: _extends({
    transition: theme.transitions.create(["transform", "opacity"], {
      duration: theme.transitions.duration.shorter
    }),
    opacity: 1
  }, !ownerState.open && {
    opacity: 0,
    transform: "scale(0.5)"
  }, ownerState.tooltipPlacement === "left" && {
    transformOrigin: "100% 50%",
    right: "100%",
    marginRight: 8
  }, ownerState.tooltipPlacement === "right" && {
    transformOrigin: "0% 50%",
    left: "100%",
    marginLeft: 8
  })
}));
var SpeedDialActionStaticTooltipLabel = styled_default("span", {
  name: "MuiSpeedDialAction",
  slot: "StaticTooltipLabel",
  overridesResolver: (props, styles) => styles.staticTooltipLabel
})(({
  theme
}) => _extends({
  position: "absolute"
}, theme.typography.body1, {
  backgroundColor: (theme.vars || theme).palette.background.paper,
  borderRadius: (theme.vars || theme).shape.borderRadius,
  boxShadow: (theme.vars || theme).shadows[1],
  color: (theme.vars || theme).palette.text.secondary,
  padding: "4px 16px",
  wordBreak: "keep-all"
}));
var SpeedDialAction = React.forwardRef(function SpeedDialAction2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiSpeedDialAction"
  });
  const {
    className,
    delay = 0,
    FabProps = {},
    icon,
    id,
    open,
    TooltipClasses,
    tooltipOpen: tooltipOpenProp = false,
    tooltipPlacement = "left",
    tooltipTitle
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded);
  const ownerState = _extends({}, props, {
    tooltipPlacement
  });
  const classes = useUtilityClasses(ownerState);
  const [tooltipOpen, setTooltipOpen] = React.useState(tooltipOpenProp);
  const handleTooltipClose = () => {
    setTooltipOpen(false);
  };
  const handleTooltipOpen = () => {
    setTooltipOpen(true);
  };
  const transitionStyle = {
    transitionDelay: `${delay}ms`
  };
  const fab = (0, import_jsx_runtime.jsx)(SpeedDialActionFab, _extends({
    size: "small",
    className: clsx_default(classes.fab, className),
    tabIndex: -1,
    role: "menuitem",
    ownerState
  }, FabProps, {
    style: _extends({}, transitionStyle, FabProps.style),
    children: icon
  }));
  if (tooltipOpenProp) {
    return (0, import_jsx_runtime2.jsxs)(SpeedDialActionStaticTooltip, _extends({
      id,
      ref,
      className: classes.staticTooltip,
      ownerState
    }, other, {
      children: [(0, import_jsx_runtime.jsx)(SpeedDialActionStaticTooltipLabel, {
        style: transitionStyle,
        id: `${id}-label`,
        className: classes.staticTooltipLabel,
        ownerState,
        children: tooltipTitle
      }), React.cloneElement(fab, {
        "aria-labelledby": `${id}-label`
      })]
    }));
  }
  if (!open && tooltipOpen) {
    setTooltipOpen(false);
  }
  return (0, import_jsx_runtime.jsx)(Tooltip_default, _extends({
    id,
    ref,
    title: tooltipTitle,
    placement: tooltipPlacement,
    onClose: handleTooltipClose,
    onOpen: handleTooltipOpen,
    open: open && tooltipOpen,
    classes: TooltipClasses
  }, other, {
    children: fab
  }));
});
true ? SpeedDialAction.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types.default.object,
  /**
   * @ignore
   */
  className: import_prop_types.default.string,
  /**
   * Adds a transition delay, to allow a series of SpeedDialActions to be animated.
   * @default 0
   */
  delay: import_prop_types.default.number,
  /**
   * Props applied to the [`Fab`](/material-ui/api/fab/) component.
   * @default {}
   */
  FabProps: import_prop_types.default.object,
  /**
   * The icon to display in the SpeedDial Fab.
   */
  icon: import_prop_types.default.node,
  /**
   * This prop is used to help implement the accessibility logic.
   * If you don't provide this prop. It falls back to a randomly generated id.
   */
  id: import_prop_types.default.string,
  /**
   * If `true`, the component is shown.
   */
  open: import_prop_types.default.bool,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types.default.oneOfType([import_prop_types.default.arrayOf(import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object, import_prop_types.default.bool])), import_prop_types.default.func, import_prop_types.default.object]),
  /**
   * `classes` prop applied to the [`Tooltip`](/material-ui/api/tooltip/) element.
   */
  TooltipClasses: import_prop_types.default.object,
  /**
   * Make the tooltip always visible when the SpeedDial is open.
   * @default false
   */
  tooltipOpen: import_prop_types.default.bool,
  /**
   * Placement of the tooltip.
   * @default 'left'
   */
  tooltipPlacement: import_prop_types.default.oneOf(["bottom-end", "bottom-start", "bottom", "left-end", "left-start", "left", "right-end", "right-start", "right", "top-end", "top-start", "top"]),
  /**
   * Label to display in the tooltip.
   */
  tooltipTitle: import_prop_types.default.node
} : void 0;
var SpeedDialAction_default = SpeedDialAction;

export {
  getSpeedDialActionUtilityClass,
  speedDialActionClasses_default,
  SpeedDialAction_default
};
//# sourceMappingURL=chunk-6L2HGHQW.js.map
