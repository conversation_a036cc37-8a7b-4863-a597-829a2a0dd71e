import {
  HTMLElementType,
  Timeout,
  createChainedFunction,
  debounce,
  deprecatedPropType,
  detectScrollType,
  elementAcceptingRef_default,
  elementTypeAcceptingRef_default,
  exactProp,
  getNormalizedScrollLeft,
  getScrollbarSize,
  ownerDocument,
  ownerWindow,
  ponyfillGlobal_default,
  refType_default,
  requirePropFactory,
  unsupportedProp,
  useEventCallback_default,
  useIsFocusVisible,
  useLazyRef,
  useOnMount,
  usePreviousProps_default,
  useTimeout,
  visuallyHidden_default
} from "./chunk-EXKCK6CI.js";
import {
  useEnhancedEffect_default
} from "./chunk-56VEPRXN.js";
import {
  integerPropType_default
} from "./chunk-XYO5E4UC.js";
import {
  setRef,
  useControlled,
  useForkRef,
  useId
} from "./chunk-RCV74CT4.js";
import {
  isMuiElement
} from "./chunk-EDHI6VEC.js";
import {
  getValidReactChildren
} from "./chunk-3UUYZXPW.js";
import {
  chainPropTypes
} from "./chunk-HTVIEQAM.js";
import {
  ClassNameGenerator_default,
  clamp_default,
  composeClasses,
  generateUtilityClass,
  generateUtilityClasses,
  getDisplayName,
  globalStateClasses,
  isGlobalState
} from "./chunk-EH52VBW6.js";
import {
  deepmerge,
  isPlainObject,
  resolveProps
} from "./chunk-S3K3XWQY.js";
import "./chunk-MDE6ZET7.js";
import "./chunk-CMWVKTKB.js";
import {
  capitalize,
  formatMuiErrorMessage
} from "./chunk-OEFUZPWO.js";
import "./chunk-NZ77J7BH.js";
import "./chunk-4GAI7T4A.js";
import "./chunk-R56R2YIZ.js";
import "./chunk-BYPFWIQ6.js";
export {
  HTMLElementType,
  chainPropTypes,
  clamp_default as clamp,
  deepmerge,
  elementAcceptingRef_default as elementAcceptingRef,
  elementTypeAcceptingRef_default as elementTypeAcceptingRef,
  exactProp,
  formatMuiErrorMessage,
  getDisplayName,
  getValidReactChildren,
  globalStateClasses,
  integerPropType_default as integerPropType,
  resolveProps as internal_resolveProps,
  isGlobalState,
  isPlainObject,
  ponyfillGlobal_default as ponyfillGlobal,
  refType_default as refType,
  ClassNameGenerator_default as unstable_ClassNameGenerator,
  Timeout as unstable_Timeout,
  capitalize as unstable_capitalize,
  composeClasses as unstable_composeClasses,
  createChainedFunction as unstable_createChainedFunction,
  debounce as unstable_debounce,
  deprecatedPropType as unstable_deprecatedPropType,
  detectScrollType as unstable_detectScrollType,
  generateUtilityClass as unstable_generateUtilityClass,
  generateUtilityClasses as unstable_generateUtilityClasses,
  getNormalizedScrollLeft as unstable_getNormalizedScrollLeft,
  getScrollbarSize as unstable_getScrollbarSize,
  isGlobalState as unstable_isGlobalState,
  isMuiElement as unstable_isMuiElement,
  ownerDocument as unstable_ownerDocument,
  ownerWindow as unstable_ownerWindow,
  requirePropFactory as unstable_requirePropFactory,
  setRef as unstable_setRef,
  unsupportedProp as unstable_unsupportedProp,
  useControlled as unstable_useControlled,
  useEnhancedEffect_default as unstable_useEnhancedEffect,
  useEventCallback_default as unstable_useEventCallback,
  useForkRef as unstable_useForkRef,
  useId as unstable_useId,
  useIsFocusVisible as unstable_useIsFocusVisible,
  useLazyRef as unstable_useLazyRef,
  useOnMount as unstable_useOnMount,
  useTimeout as unstable_useTimeout,
  usePreviousProps_default as usePreviousProps,
  visuallyHidden_default as visuallyHidden
};
//# sourceMappingURL=@mui_utils.js.map
