{"version": 3, "sources": ["../../@mui/lab/LoadingButton/LoadingButton.js", "../../@mui/lab/LoadingButton/loadingButtonClasses.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"disabled\", \"id\", \"loading\", \"loadingIndicator\", \"loadingPosition\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { chainPropTypes } from '@mui/utils';\nimport { capitalize, unstable_useId as useId } from '@mui/material/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport Button from '@mui/material/Button';\nimport { ButtonGroupContext } from '@mui/material/ButtonGroup';\nimport CircularProgress from '@mui/material/CircularProgress';\nimport resolveProps from '@mui/utils/resolveProps';\nimport loadingButtonClasses, { getLoadingButtonUtilityClass } from './loadingButtonClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    loading,\n    loadingPosition,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', loading && 'loading'],\n    startIcon: [loading && `startIconLoading${capitalize(loadingPosition)}`],\n    endIcon: [loading && `endIconLoading${capitalize(loadingPosition)}`],\n    loadingIndicator: ['loadingIndicator', loading && `loadingIndicator${capitalize(loadingPosition)}`]\n  };\n  const composedClasses = composeClasses(slots, getLoadingButtonUtilityClass, classes);\n  return _extends({}, classes, composedClasses);\n};\n\n// TODO use `import { rootShouldForwardProp } from '../styles/styled';` once move to core\nconst rootShouldForwardProp = prop => prop !== 'ownerState' && prop !== 'theme' && prop !== 'sx' && prop !== 'as' && prop !== 'classes';\nconst LoadingButtonRoot = styled(Button, {\n  shouldForwardProp: prop => rootShouldForwardProp(prop) || prop === 'classes',\n  name: 'MuiLoadingButton',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    return [styles.root, styles.startIconLoadingStart && {\n      [`& .${loadingButtonClasses.startIconLoadingStart}`]: styles.startIconLoadingStart\n    }, styles.endIconLoadingEnd && {\n      [`& .${loadingButtonClasses.endIconLoadingEnd}`]: styles.endIconLoadingEnd\n    }];\n  }\n})(({\n  ownerState,\n  theme\n}) => _extends({\n  [`& .${loadingButtonClasses.startIconLoadingStart}, & .${loadingButtonClasses.endIconLoadingEnd}`]: {\n    transition: theme.transitions.create(['opacity'], {\n      duration: theme.transitions.duration.short\n    }),\n    opacity: 0\n  }\n}, ownerState.loadingPosition === 'center' && {\n  transition: theme.transitions.create(['background-color', 'box-shadow', 'border-color'], {\n    duration: theme.transitions.duration.short\n  }),\n  [`&.${loadingButtonClasses.loading}`]: {\n    color: 'transparent'\n  }\n}, ownerState.loadingPosition === 'start' && ownerState.fullWidth && {\n  [`& .${loadingButtonClasses.startIconLoadingStart}, & .${loadingButtonClasses.endIconLoadingEnd}`]: {\n    transition: theme.transitions.create(['opacity'], {\n      duration: theme.transitions.duration.short\n    }),\n    opacity: 0,\n    marginRight: -8\n  }\n}, ownerState.loadingPosition === 'end' && ownerState.fullWidth && {\n  [`& .${loadingButtonClasses.startIconLoadingStart}, & .${loadingButtonClasses.endIconLoadingEnd}`]: {\n    transition: theme.transitions.create(['opacity'], {\n      duration: theme.transitions.duration.short\n    }),\n    opacity: 0,\n    marginLeft: -8\n  }\n}));\nconst LoadingButtonLoadingIndicator = styled('span', {\n  name: 'MuiLoadingButton',\n  slot: 'LoadingIndicator',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.loadingIndicator, styles[`loadingIndicator${capitalize(ownerState.loadingPosition)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  position: 'absolute',\n  visibility: 'visible',\n  display: 'flex'\n}, ownerState.loadingPosition === 'start' && (ownerState.variant === 'outlined' || ownerState.variant === 'contained') && {\n  left: ownerState.size === 'small' ? 10 : 14\n}, ownerState.loadingPosition === 'start' && ownerState.variant === 'text' && {\n  left: 6\n}, ownerState.loadingPosition === 'center' && {\n  left: '50%',\n  transform: 'translate(-50%)',\n  color: (theme.vars || theme).palette.action.disabled\n}, ownerState.loadingPosition === 'end' && (ownerState.variant === 'outlined' || ownerState.variant === 'contained') && {\n  right: ownerState.size === 'small' ? 10 : 14\n}, ownerState.loadingPosition === 'end' && ownerState.variant === 'text' && {\n  right: 6\n}, ownerState.loadingPosition === 'start' && ownerState.fullWidth && {\n  position: 'relative',\n  left: -10\n}, ownerState.loadingPosition === 'end' && ownerState.fullWidth && {\n  position: 'relative',\n  right: -10\n}));\nconst LoadingButton = /*#__PURE__*/React.forwardRef(function LoadingButton(inProps, ref) {\n  const contextProps = React.useContext(ButtonGroupContext);\n  const resolvedProps = resolveProps(contextProps, inProps);\n  const props = useThemeProps({\n    props: resolvedProps,\n    name: 'MuiLoadingButton'\n  });\n  const {\n      children,\n      disabled = false,\n      id: idProp,\n      loading = false,\n      loadingIndicator: loadingIndicatorProp,\n      loadingPosition = 'center',\n      variant = 'text'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const id = useId(idProp);\n  const loadingIndicator = loadingIndicatorProp != null ? loadingIndicatorProp : /*#__PURE__*/_jsx(CircularProgress, {\n    \"aria-labelledby\": id,\n    color: \"inherit\",\n    size: 16\n  });\n  const ownerState = _extends({}, props, {\n    disabled,\n    loading,\n    loadingIndicator,\n    loadingPosition,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const loadingButtonLoadingIndicator = loading ? /*#__PURE__*/_jsx(LoadingButtonLoadingIndicator, {\n    className: classes.loadingIndicator,\n    ownerState: ownerState,\n    children: loadingIndicator\n  }) : null;\n  return /*#__PURE__*/_jsxs(LoadingButtonRoot, _extends({\n    disabled: disabled || loading,\n    id: id,\n    ref: ref\n  }, other, {\n    variant: variant,\n    classes: classes,\n    ownerState: ownerState,\n    children: [ownerState.loadingPosition === 'end' ? children : loadingButtonLoadingIndicator, ownerState.loadingPosition === 'end' ? loadingButtonLoadingIndicator : children]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? LoadingButton.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  id: PropTypes.string,\n  /**\n   * If `true`, the loading indicator is shown and the button becomes disabled.\n   * @default false\n   */\n  loading: PropTypes.bool,\n  /**\n   * Element placed before the children if the button is in loading state.\n   * The node should contain an element with `role=\"progressbar\"` with an accessible name.\n   * By default we render a `CircularProgress` that is labelled by the button itself.\n   * @default <CircularProgress color=\"inherit\" size={16} />\n   */\n  loadingIndicator: PropTypes.node,\n  /**\n   * The loading indicator can be positioned on the start, end, or the center of the button.\n   * @default 'center'\n   */\n  loadingPosition: chainPropTypes(PropTypes.oneOf(['start', 'end', 'center']), props => {\n    if (props.loadingPosition === 'start' && !props.startIcon) {\n      return new Error(`MUI: The loadingPosition=\"start\" should be used in combination with startIcon.`);\n    }\n    if (props.loadingPosition === 'end' && !props.endIcon) {\n      return new Error(`MUI: The loadingPosition=\"end\" should be used in combination with endIcon.`);\n    }\n    return null;\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'text'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['contained', 'outlined', 'text']), PropTypes.string])\n} : void 0;\nexport default LoadingButton;", "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getLoadingButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiLoadingButton', slot);\n}\nconst loadingButtonClasses = generateUtilityClasses('MuiLoadingButton', ['root', 'loading', 'loadingIndicator', 'loadingIndicatorCenter', 'loadingIndicatorStart', 'loadingIndicatorEnd', 'endIconLoadingEnd', 'startIconLoadingStart']);\nexport default loadingButtonClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AAEA,YAAuB;AACvB,wBAAsB;;;ACJf,SAAS,6BAA6B,MAAM;AACjD,SAAO,qBAAqB,oBAAoB,IAAI;AACtD;AACA,IAAM,uBAAuB,uBAAuB,oBAAoB,CAAC,QAAQ,WAAW,oBAAoB,0BAA0B,yBAAyB,uBAAuB,qBAAqB,uBAAuB,CAAC;AACvO,IAAO,+BAAQ;;;ADUf,yBAA4B;AAC5B,IAAAA,sBAA8B;AAb9B,IAAM,YAAY,CAAC,YAAY,YAAY,MAAM,WAAW,oBAAoB,mBAAmB,SAAS;AAc5G,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,WAAW,SAAS;AAAA,IACnC,WAAW,CAAC,WAAW,mBAAmB,mBAAW,eAAe,CAAC,EAAE;AAAA,IACvE,SAAS,CAAC,WAAW,iBAAiB,mBAAW,eAAe,CAAC,EAAE;AAAA,IACnE,kBAAkB,CAAC,oBAAoB,WAAW,mBAAmB,mBAAW,eAAe,CAAC,EAAE;AAAA,EACpG;AACA,QAAM,kBAAkB,eAAe,OAAO,8BAA8B,OAAO;AACnF,SAAO,SAAS,CAAC,GAAG,SAAS,eAAe;AAC9C;AAGA,IAAM,wBAAwB,UAAQ,SAAS,gBAAgB,SAAS,WAAW,SAAS,QAAQ,SAAS,QAAQ,SAAS;AAC9H,IAAM,oBAAoB,eAAO,gBAAQ;AAAA,EACvC,mBAAmB,UAAQ,sBAAsB,IAAI,KAAK,SAAS;AAAA,EACnE,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,WAAO,CAAC,OAAO,MAAM,OAAO,yBAAyB;AAAA,MACnD,CAAC,MAAM,6BAAqB,qBAAqB,EAAE,GAAG,OAAO;AAAA,IAC/D,GAAG,OAAO,qBAAqB;AAAA,MAC7B,CAAC,MAAM,6BAAqB,iBAAiB,EAAE,GAAG,OAAO;AAAA,IAC3D,CAAC;AAAA,EACH;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AAAA,EACA;AACF,MAAM,SAAS;AAAA,EACb,CAAC,MAAM,6BAAqB,qBAAqB,QAAQ,6BAAqB,iBAAiB,EAAE,GAAG;AAAA,IAClG,YAAY,MAAM,YAAY,OAAO,CAAC,SAAS,GAAG;AAAA,MAChD,UAAU,MAAM,YAAY,SAAS;AAAA,IACvC,CAAC;AAAA,IACD,SAAS;AAAA,EACX;AACF,GAAG,WAAW,oBAAoB,YAAY;AAAA,EAC5C,YAAY,MAAM,YAAY,OAAO,CAAC,oBAAoB,cAAc,cAAc,GAAG;AAAA,IACvF,UAAU,MAAM,YAAY,SAAS;AAAA,EACvC,CAAC;AAAA,EACD,CAAC,KAAK,6BAAqB,OAAO,EAAE,GAAG;AAAA,IACrC,OAAO;AAAA,EACT;AACF,GAAG,WAAW,oBAAoB,WAAW,WAAW,aAAa;AAAA,EACnE,CAAC,MAAM,6BAAqB,qBAAqB,QAAQ,6BAAqB,iBAAiB,EAAE,GAAG;AAAA,IAClG,YAAY,MAAM,YAAY,OAAO,CAAC,SAAS,GAAG;AAAA,MAChD,UAAU,MAAM,YAAY,SAAS;AAAA,IACvC,CAAC;AAAA,IACD,SAAS;AAAA,IACT,aAAa;AAAA,EACf;AACF,GAAG,WAAW,oBAAoB,SAAS,WAAW,aAAa;AAAA,EACjE,CAAC,MAAM,6BAAqB,qBAAqB,QAAQ,6BAAqB,iBAAiB,EAAE,GAAG;AAAA,IAClG,YAAY,MAAM,YAAY,OAAO,CAAC,SAAS,GAAG;AAAA,MAChD,UAAU,MAAM,YAAY,SAAS;AAAA,IACvC,CAAC;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AACF,CAAC,CAAC;AACF,IAAM,gCAAgC,eAAO,QAAQ;AAAA,EACnD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,kBAAkB,OAAO,mBAAmB,mBAAW,WAAW,eAAe,CAAC,EAAE,CAAC;AAAA,EACtG;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AAAA,EACA;AACF,MAAM,SAAS;AAAA,EACb,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,SAAS;AACX,GAAG,WAAW,oBAAoB,YAAY,WAAW,YAAY,cAAc,WAAW,YAAY,gBAAgB;AAAA,EACxH,MAAM,WAAW,SAAS,UAAU,KAAK;AAC3C,GAAG,WAAW,oBAAoB,WAAW,WAAW,YAAY,UAAU;AAAA,EAC5E,MAAM;AACR,GAAG,WAAW,oBAAoB,YAAY;AAAA,EAC5C,MAAM;AAAA,EACN,WAAW;AAAA,EACX,QAAQ,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAC9C,GAAG,WAAW,oBAAoB,UAAU,WAAW,YAAY,cAAc,WAAW,YAAY,gBAAgB;AAAA,EACtH,OAAO,WAAW,SAAS,UAAU,KAAK;AAC5C,GAAG,WAAW,oBAAoB,SAAS,WAAW,YAAY,UAAU;AAAA,EAC1E,OAAO;AACT,GAAG,WAAW,oBAAoB,WAAW,WAAW,aAAa;AAAA,EACnE,UAAU;AAAA,EACV,MAAM;AACR,GAAG,WAAW,oBAAoB,SAAS,WAAW,aAAa;AAAA,EACjE,UAAU;AAAA,EACV,OAAO;AACT,CAAC,CAAC;AACF,IAAM,gBAAmC,iBAAW,SAASC,eAAc,SAAS,KAAK;AACvF,QAAM,eAAqB,iBAAW,0BAAkB;AACxD,QAAM,gBAAgB,aAAa,cAAc,OAAO;AACxD,QAAM,QAAQ,cAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF;AAAA,IACA,WAAW;AAAA,IACX,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,UAAU;AAAA,EACZ,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM,KAAK,cAAM,MAAM;AACvB,QAAM,mBAAmB,wBAAwB,OAAO,2BAAoC,mBAAAC,KAAK,0BAAkB;AAAA,IACjH,mBAAmB;AAAA,IACnB,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,gCAAgC,cAAuB,mBAAAA,KAAK,+BAA+B;AAAA,IAC/F,WAAW,QAAQ;AAAA,IACnB;AAAA,IACA,UAAU;AAAA,EACZ,CAAC,IAAI;AACL,aAAoB,oBAAAC,MAAM,mBAAmB,SAAS;AAAA,IACpD,UAAU,YAAY;AAAA,IACtB;AAAA,IACA;AAAA,EACF,GAAG,OAAO;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,CAAC,WAAW,oBAAoB,QAAQ,WAAW,+BAA+B,WAAW,oBAAoB,QAAQ,gCAAgC,QAAQ;AAAA,EAC7K,CAAC,CAAC;AACJ,CAAC;AACD,OAAwC,cAAc,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQvF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,IAAI,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOnB,kBAAkB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B,iBAAiB,eAAe,kBAAAA,QAAU,MAAM,CAAC,SAAS,OAAO,QAAQ,CAAC,GAAG,WAAS;AACpF,QAAI,MAAM,oBAAoB,WAAW,CAAC,MAAM,WAAW;AACzD,aAAO,IAAI,MAAM,gFAAgF;AAAA,IACnG;AACA,QAAI,MAAM,oBAAoB,SAAS,CAAC,MAAM,SAAS;AACrD,aAAO,IAAI,MAAM,4EAA4E;AAAA,IAC/F;AACA,WAAO;AAAA,EACT,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtJ,SAAS,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,aAAa,YAAY,MAAM,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAC3I,IAAI;AACJ,IAAO,wBAAQ;", "names": ["import_jsx_runtime", "LoadingButton", "_jsx", "_jsxs", "PropTypes"]}