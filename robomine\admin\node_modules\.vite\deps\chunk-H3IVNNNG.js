import {
  Zoom_default
} from "./chunk-P32F2J2W.js";
import {
  Fab_default
} from "./chunk-5QQLG6AW.js";
import {
  isMuiElement_default
} from "./chunk-P3F2W34V.js";
import {
  useControlled_default
} from "./chunk-G4V5HJJ6.js";
import {
  capitalize_default
} from "./chunk-QGOONFNA.js";
import {
  useForkRef_default
} from "./chunk-ZEHHXLMY.js";
import {
  useTheme
} from "./chunk-KVEADFL7.js";
import {
  styled_default
} from "./chunk-KIJLS2TV.js";
import {
  useThemeProps
} from "./chunk-ZYUAWKJJ.js";
import {
  useTimeout
} from "./chunk-EXKCK6CI.js";
import {
  clsx_default
} from "./chunk-YV3COZNF.js";
import {
  clamp_default,
  composeClasses,
  generateUtilityClass,
  generateUtilityClasses,
  init_clamp
} from "./chunk-EH52VBW6.js";
import {
  require_prop_types
} from "./chunk-MDE6ZET7.js";
import {
  _objectWithoutPropertiesLoose
} from "./chunk-OBSDRUBD.js";
import {
  require_react_is
} from "./chunk-NZ77J7BH.js";
import {
  require_jsx_runtime
} from "./chunk-D4DBS43D.js";
import {
  _extends,
  init_extends
} from "./chunk-4GAI7T4A.js";
import {
  require_react
} from "./chunk-R56R2YIZ.js";
import {
  __toESM
} from "./chunk-BYPFWIQ6.js";

// node_modules/@mui/material/SpeedDial/SpeedDial.js
init_extends();
var React = __toESM(require_react());
var import_react_is = __toESM(require_react_is());
var import_prop_types = __toESM(require_prop_types());
init_clamp();

// node_modules/@mui/material/SpeedDial/speedDialClasses.js
function getSpeedDialUtilityClass(slot) {
  return generateUtilityClass("MuiSpeedDial", slot);
}
var speedDialClasses = generateUtilityClasses("MuiSpeedDial", ["root", "fab", "directionUp", "directionDown", "directionLeft", "directionRight", "actions", "actionsClosed"]);
var speedDialClasses_default = speedDialClasses;

// node_modules/@mui/material/SpeedDial/SpeedDial.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var _excluded = ["ref"];
var _excluded2 = ["ariaLabel", "FabProps", "children", "className", "direction", "hidden", "icon", "onBlur", "onClose", "onFocus", "onKeyDown", "onMouseEnter", "onMouseLeave", "onOpen", "open", "openIcon", "TransitionComponent", "transitionDuration", "TransitionProps"];
var _excluded3 = ["ref"];
var useUtilityClasses = (ownerState) => {
  const {
    classes,
    open,
    direction
  } = ownerState;
  const slots = {
    root: ["root", `direction${capitalize_default(direction)}`],
    fab: ["fab"],
    actions: ["actions", !open && "actionsClosed"]
  };
  return composeClasses(slots, getSpeedDialUtilityClass, classes);
};
function getOrientation(direction) {
  if (direction === "up" || direction === "down") {
    return "vertical";
  }
  if (direction === "right" || direction === "left") {
    return "horizontal";
  }
  return void 0;
}
var dialRadius = 32;
var spacingActions = 16;
var SpeedDialRoot = styled_default("div", {
  name: "MuiSpeedDial",
  slot: "Root",
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [styles.root, styles[`direction${capitalize_default(ownerState.direction)}`]];
  }
})(({
  theme,
  ownerState
}) => _extends({
  zIndex: (theme.vars || theme).zIndex.speedDial,
  display: "flex",
  alignItems: "center",
  pointerEvents: "none"
}, ownerState.direction === "up" && {
  flexDirection: "column-reverse",
  [`& .${speedDialClasses_default.actions}`]: {
    flexDirection: "column-reverse",
    marginBottom: -dialRadius,
    paddingBottom: spacingActions + dialRadius
  }
}, ownerState.direction === "down" && {
  flexDirection: "column",
  [`& .${speedDialClasses_default.actions}`]: {
    flexDirection: "column",
    marginTop: -dialRadius,
    paddingTop: spacingActions + dialRadius
  }
}, ownerState.direction === "left" && {
  flexDirection: "row-reverse",
  [`& .${speedDialClasses_default.actions}`]: {
    flexDirection: "row-reverse",
    marginRight: -dialRadius,
    paddingRight: spacingActions + dialRadius
  }
}, ownerState.direction === "right" && {
  flexDirection: "row",
  [`& .${speedDialClasses_default.actions}`]: {
    flexDirection: "row",
    marginLeft: -dialRadius,
    paddingLeft: spacingActions + dialRadius
  }
}));
var SpeedDialFab = styled_default(Fab_default, {
  name: "MuiSpeedDial",
  slot: "Fab",
  overridesResolver: (props, styles) => styles.fab
})(() => ({
  pointerEvents: "auto"
}));
var SpeedDialActions = styled_default("div", {
  name: "MuiSpeedDial",
  slot: "Actions",
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [styles.actions, !ownerState.open && styles.actionsClosed];
  }
})(({
  ownerState
}) => _extends({
  display: "flex",
  pointerEvents: "auto"
}, !ownerState.open && {
  transition: "top 0s linear 0.2s",
  pointerEvents: "none"
}));
var SpeedDial = React.forwardRef(function SpeedDial2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiSpeedDial"
  });
  const theme = useTheme();
  const defaultTransitionDuration = {
    enter: theme.transitions.duration.enteringScreen,
    exit: theme.transitions.duration.leavingScreen
  };
  const {
    ariaLabel,
    FabProps: {
      ref: origDialButtonRef
    } = {},
    children: childrenProp,
    className,
    direction = "up",
    hidden = false,
    icon,
    onBlur,
    onClose,
    onFocus,
    onKeyDown,
    onMouseEnter,
    onMouseLeave,
    onOpen,
    open: openProp,
    TransitionComponent = Zoom_default,
    transitionDuration = defaultTransitionDuration,
    TransitionProps
  } = props, FabProps = _objectWithoutPropertiesLoose(props.FabProps, _excluded), other = _objectWithoutPropertiesLoose(props, _excluded2);
  const [open, setOpenState] = useControlled_default({
    controlled: openProp,
    default: false,
    name: "SpeedDial",
    state: "open"
  });
  const ownerState = _extends({}, props, {
    open,
    direction
  });
  const classes = useUtilityClasses(ownerState);
  const eventTimer = useTimeout();
  const focusedAction = React.useRef(0);
  const nextItemArrowKey = React.useRef();
  const actions = React.useRef([]);
  actions.current = [actions.current[0]];
  const handleOwnFabRef = React.useCallback((fabFef) => {
    actions.current[0] = fabFef;
  }, []);
  const handleFabRef = useForkRef_default(origDialButtonRef, handleOwnFabRef);
  const createHandleSpeedDialActionButtonRef = (dialActionIndex, origButtonRef) => {
    return (buttonRef) => {
      actions.current[dialActionIndex + 1] = buttonRef;
      if (origButtonRef) {
        origButtonRef(buttonRef);
      }
    };
  };
  const handleKeyDown = (event) => {
    if (onKeyDown) {
      onKeyDown(event);
    }
    const key = event.key.replace("Arrow", "").toLowerCase();
    const {
      current: nextItemArrowKeyCurrent = key
    } = nextItemArrowKey;
    if (event.key === "Escape") {
      setOpenState(false);
      actions.current[0].focus();
      if (onClose) {
        onClose(event, "escapeKeyDown");
      }
      return;
    }
    if (getOrientation(key) === getOrientation(nextItemArrowKeyCurrent) && getOrientation(key) !== void 0) {
      event.preventDefault();
      const actionStep = key === nextItemArrowKeyCurrent ? 1 : -1;
      const nextAction = clamp_default(focusedAction.current + actionStep, 0, actions.current.length - 1);
      actions.current[nextAction].focus();
      focusedAction.current = nextAction;
      nextItemArrowKey.current = nextItemArrowKeyCurrent;
    }
  };
  React.useEffect(() => {
    if (!open) {
      focusedAction.current = 0;
      nextItemArrowKey.current = void 0;
    }
  }, [open]);
  const handleClose = (event) => {
    if (event.type === "mouseleave" && onMouseLeave) {
      onMouseLeave(event);
    }
    if (event.type === "blur" && onBlur) {
      onBlur(event);
    }
    eventTimer.clear();
    if (event.type === "blur") {
      eventTimer.start(0, () => {
        setOpenState(false);
        if (onClose) {
          onClose(event, "blur");
        }
      });
    } else {
      setOpenState(false);
      if (onClose) {
        onClose(event, "mouseLeave");
      }
    }
  };
  const handleClick = (event) => {
    if (FabProps.onClick) {
      FabProps.onClick(event);
    }
    eventTimer.clear();
    if (open) {
      setOpenState(false);
      if (onClose) {
        onClose(event, "toggle");
      }
    } else {
      setOpenState(true);
      if (onOpen) {
        onOpen(event, "toggle");
      }
    }
  };
  const handleOpen = (event) => {
    if (event.type === "mouseenter" && onMouseEnter) {
      onMouseEnter(event);
    }
    if (event.type === "focus" && onFocus) {
      onFocus(event);
    }
    eventTimer.clear();
    if (!open) {
      eventTimer.start(0, () => {
        setOpenState(true);
        if (onOpen) {
          const eventMap = {
            focus: "focus",
            mouseenter: "mouseEnter"
          };
          onOpen(event, eventMap[event.type]);
        }
      });
    }
  };
  const id = ariaLabel.replace(/^[^a-z]+|[^\w:.-]+/gi, "");
  const allItems = React.Children.toArray(childrenProp).filter((child) => {
    if (true) {
      if ((0, import_react_is.isFragment)(child)) {
        console.error(["MUI: The SpeedDial component doesn't accept a Fragment as a child.", "Consider providing an array instead."].join("\n"));
      }
    }
    return React.isValidElement(child);
  });
  const children = allItems.map((child, index) => {
    const _child$props = child.props, {
      FabProps: {
        ref: origButtonRef
      } = {},
      tooltipPlacement: tooltipPlacementProp
    } = _child$props, ChildFabProps = _objectWithoutPropertiesLoose(_child$props.FabProps, _excluded3);
    const tooltipPlacement = tooltipPlacementProp || (getOrientation(direction) === "vertical" ? "left" : "top");
    return React.cloneElement(child, {
      FabProps: _extends({}, ChildFabProps, {
        ref: createHandleSpeedDialActionButtonRef(index, origButtonRef)
      }),
      delay: 30 * (open ? index : allItems.length - index),
      open,
      tooltipPlacement,
      id: `${id}-action-${index}`
    });
  });
  return (0, import_jsx_runtime2.jsxs)(SpeedDialRoot, _extends({
    className: clsx_default(classes.root, className),
    ref,
    role: "presentation",
    onKeyDown: handleKeyDown,
    onBlur: handleClose,
    onFocus: handleOpen,
    onMouseEnter: handleOpen,
    onMouseLeave: handleClose,
    ownerState
  }, other, {
    children: [(0, import_jsx_runtime.jsx)(TransitionComponent, _extends({
      in: !hidden,
      timeout: transitionDuration,
      unmountOnExit: true
    }, TransitionProps, {
      children: (0, import_jsx_runtime.jsx)(SpeedDialFab, _extends({
        color: "primary",
        "aria-label": ariaLabel,
        "aria-haspopup": "true",
        "aria-expanded": open,
        "aria-controls": `${id}-actions`
      }, FabProps, {
        onClick: handleClick,
        className: clsx_default(classes.fab, FabProps.className),
        ref: handleFabRef,
        ownerState,
        children: React.isValidElement(icon) && isMuiElement_default(icon, ["SpeedDialIcon"]) ? React.cloneElement(icon, {
          open
        }) : icon
      }))
    })), (0, import_jsx_runtime.jsx)(SpeedDialActions, {
      id: `${id}-actions`,
      role: "menu",
      "aria-orientation": getOrientation(direction),
      className: clsx_default(classes.actions, !open && classes.actionsClosed),
      ownerState,
      children
    })]
  }));
});
true ? SpeedDial.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The aria-label of the button element.
   * Also used to provide the `id` for the `SpeedDial` element and its children.
   */
  ariaLabel: import_prop_types.default.string.isRequired,
  /**
   * SpeedDialActions to display when the SpeedDial is `open`.
   */
  children: import_prop_types.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types.default.object,
  /**
   * @ignore
   */
  className: import_prop_types.default.string,
  /**
   * The direction the actions open relative to the floating action button.
   * @default 'up'
   */
  direction: import_prop_types.default.oneOf(["down", "left", "right", "up"]),
  /**
   * Props applied to the [`Fab`](/material-ui/api/fab/) element.
   * @default {}
   */
  FabProps: import_prop_types.default.object,
  /**
   * If `true`, the SpeedDial is hidden.
   * @default false
   */
  hidden: import_prop_types.default.bool,
  /**
   * The icon to display in the SpeedDial Fab. The `SpeedDialIcon` component
   * provides a default Icon with animation.
   */
  icon: import_prop_types.default.node,
  /**
   * @ignore
   */
  onBlur: import_prop_types.default.func,
  /**
   * Callback fired when the component requests to be closed.
   *
   * @param {object} event The event source of the callback.
   * @param {string} reason Can be: `"toggle"`, `"blur"`, `"mouseLeave"`, `"escapeKeyDown"`.
   */
  onClose: import_prop_types.default.func,
  /**
   * @ignore
   */
  onFocus: import_prop_types.default.func,
  /**
   * @ignore
   */
  onKeyDown: import_prop_types.default.func,
  /**
   * @ignore
   */
  onMouseEnter: import_prop_types.default.func,
  /**
   * @ignore
   */
  onMouseLeave: import_prop_types.default.func,
  /**
   * Callback fired when the component requests to be open.
   *
   * @param {object} event The event source of the callback.
   * @param {string} reason Can be: `"toggle"`, `"focus"`, `"mouseEnter"`.
   */
  onOpen: import_prop_types.default.func,
  /**
   * If `true`, the component is shown.
   */
  open: import_prop_types.default.bool,
  /**
   * The icon to display in the SpeedDial Fab when the SpeedDial is open.
   */
  openIcon: import_prop_types.default.node,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types.default.oneOfType([import_prop_types.default.arrayOf(import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object, import_prop_types.default.bool])), import_prop_types.default.func, import_prop_types.default.object]),
  /**
   * The component used for the transition.
   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.
   * @default Zoom
   */
  TransitionComponent: import_prop_types.default.elementType,
  /**
   * The duration for the transition, in milliseconds.
   * You may specify a single timeout for all transitions, or individually with an object.
   * @default {
   *   enter: theme.transitions.duration.enteringScreen,
   *   exit: theme.transitions.duration.leavingScreen,
   * }
   */
  transitionDuration: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.shape({
    appear: import_prop_types.default.number,
    enter: import_prop_types.default.number,
    exit: import_prop_types.default.number
  })]),
  /**
   * Props applied to the transition element.
   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.
   */
  TransitionProps: import_prop_types.default.object
} : void 0;
var SpeedDial_default = SpeedDial;

export {
  getSpeedDialUtilityClass,
  speedDialClasses_default,
  SpeedDial_default
};
//# sourceMappingURL=chunk-H3IVNNNG.js.map
