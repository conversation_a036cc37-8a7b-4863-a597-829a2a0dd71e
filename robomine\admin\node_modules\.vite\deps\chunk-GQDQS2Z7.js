import {
  createSvgIcon
} from "./chunk-2WMRNP5B.js";
import {
  styled_default
} from "./chunk-KIJLS2TV.js";
import {
  useThemeProps
} from "./chunk-ZYUAWKJJ.js";
import {
  clsx_default
} from "./chunk-YV3COZNF.js";
import {
  composeClasses,
  generateUtilityClass,
  generateUtilityClasses
} from "./chunk-EH52VBW6.js";
import {
  require_prop_types
} from "./chunk-MDE6ZET7.js";
import {
  _objectWithoutPropertiesLoose
} from "./chunk-OBSDRUBD.js";
import {
  require_jsx_runtime
} from "./chunk-D4DBS43D.js";
import {
  _extends,
  init_extends
} from "./chunk-4GAI7T4A.js";
import {
  require_react
} from "./chunk-R56R2YIZ.js";
import {
  __toESM
} from "./chunk-BYPFWIQ6.js";

// node_modules/@mui/material/SpeedDialIcon/SpeedDialIcon.js
init_extends();
var React2 = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());

// node_modules/@mui/material/internal/svg-icons/Add.js
var React = __toESM(require_react());
var import_jsx_runtime = __toESM(require_jsx_runtime());
var Add_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"
}), "Add");

// node_modules/@mui/material/SpeedDialIcon/speedDialIconClasses.js
function getSpeedDialIconUtilityClass(slot) {
  return generateUtilityClass("MuiSpeedDialIcon", slot);
}
var speedDialIconClasses = generateUtilityClasses("MuiSpeedDialIcon", ["root", "icon", "iconOpen", "iconWithOpenIconOpen", "openIcon", "openIconOpen"]);
var speedDialIconClasses_default = speedDialIconClasses;

// node_modules/@mui/material/SpeedDialIcon/SpeedDialIcon.js
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var import_jsx_runtime3 = __toESM(require_jsx_runtime());
var _excluded = ["className", "icon", "open", "openIcon"];
var useUtilityClasses = (ownerState) => {
  const {
    classes,
    open,
    openIcon
  } = ownerState;
  const slots = {
    root: ["root"],
    icon: ["icon", open && "iconOpen", openIcon && open && "iconWithOpenIconOpen"],
    openIcon: ["openIcon", open && "openIconOpen"]
  };
  return composeClasses(slots, getSpeedDialIconUtilityClass, classes);
};
var SpeedDialIconRoot = styled_default("span", {
  name: "MuiSpeedDialIcon",
  slot: "Root",
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [{
      [`& .${speedDialIconClasses_default.icon}`]: styles.icon
    }, {
      [`& .${speedDialIconClasses_default.icon}`]: ownerState.open && styles.iconOpen
    }, {
      [`& .${speedDialIconClasses_default.icon}`]: ownerState.open && ownerState.openIcon && styles.iconWithOpenIconOpen
    }, {
      [`& .${speedDialIconClasses_default.openIcon}`]: styles.openIcon
    }, {
      [`& .${speedDialIconClasses_default.openIcon}`]: ownerState.open && styles.openIconOpen
    }, styles.root];
  }
})(({
  theme,
  ownerState
}) => ({
  height: 24,
  [`& .${speedDialIconClasses_default.icon}`]: _extends({
    transition: theme.transitions.create(["transform", "opacity"], {
      duration: theme.transitions.duration.short
    })
  }, ownerState.open && _extends({
    transform: "rotate(45deg)"
  }, ownerState.openIcon && {
    opacity: 0
  })),
  [`& .${speedDialIconClasses_default.openIcon}`]: _extends({
    position: "absolute",
    transition: theme.transitions.create(["transform", "opacity"], {
      duration: theme.transitions.duration.short
    }),
    opacity: 0,
    transform: "rotate(-45deg)"
  }, ownerState.open && {
    transform: "rotate(0deg)",
    opacity: 1
  })
}));
var SpeedDialIcon = React2.forwardRef(function SpeedDialIcon2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiSpeedDialIcon"
  });
  const {
    className,
    icon: iconProp,
    openIcon: openIconProp
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded);
  const ownerState = props;
  const classes = useUtilityClasses(ownerState);
  function formatIcon(icon, newClassName) {
    if (React2.isValidElement(icon)) {
      return React2.cloneElement(icon, {
        className: newClassName
      });
    }
    return icon;
  }
  return (0, import_jsx_runtime3.jsxs)(SpeedDialIconRoot, _extends({
    className: clsx_default(classes.root, className),
    ref,
    ownerState
  }, other, {
    children: [openIconProp ? formatIcon(openIconProp, classes.openIcon) : null, iconProp ? formatIcon(iconProp, classes.icon) : (0, import_jsx_runtime2.jsx)(Add_default, {
      className: classes.icon
    })]
  }));
});
true ? SpeedDialIcon.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types.default.object,
  /**
   * @ignore
   */
  className: import_prop_types.default.string,
  /**
   * The icon to display.
   */
  icon: import_prop_types.default.node,
  /**
   * @ignore
   * If `true`, the component is shown.
   */
  open: import_prop_types.default.bool,
  /**
   * The icon to display in the SpeedDial Floating Action Button when the SpeedDial is open.
   */
  openIcon: import_prop_types.default.node,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types.default.oneOfType([import_prop_types.default.arrayOf(import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object, import_prop_types.default.bool])), import_prop_types.default.func, import_prop_types.default.object])
} : void 0;
SpeedDialIcon.muiName = "SpeedDialIcon";
var SpeedDialIcon_default = SpeedDialIcon;

export {
  getSpeedDialIconUtilityClass,
  speedDialIconClasses_default,
  SpeedDialIcon_default
};
//# sourceMappingURL=chunk-GQDQS2Z7.js.map
