{"version": 3, "sources": ["../../@mui/material/AlertTitle/AlertTitle.js", "../../@mui/material/AlertTitle/alertTitleClasses.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled, createUseThemeProps } from '../zero-styled';\nimport Typography from '../Typography';\nimport { getAlertTitleUtilityClass } from './alertTitleClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useThemeProps = createUseThemeProps('MuiAlertTitle');\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getAlertTitleUtilityClass, classes);\n};\nconst AlertTitleRoot = styled(Typography, {\n  name: 'MuiAlertTitle',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme\n}) => {\n  return {\n    fontWeight: theme.typography.fontWeightMedium,\n    marginTop: -2\n  };\n});\nconst AlertTitle = /*#__PURE__*/React.forwardRef(function AlertTitle(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiAlertTitle'\n  });\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(AlertTitleRoot, _extends({\n    gutterBottom: true,\n    component: \"div\",\n    ownerState: ownerState,\n    ref: ref,\n    className: clsx(classes.root, className)\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? AlertTitle.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default AlertTitle;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAlertTitleUtilityClass(slot) {\n  return generateUtilityClass('MuiAlertTitle', slot);\n}\nconst alertTitleClasses = generateUtilityClasses('MuiAlertTitle', ['root']);\nexport default alertTitleClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AAGA,YAAuB;AACvB,wBAAsB;;;ACJf,SAAS,0BAA0B,MAAM;AAC9C,SAAO,qBAAqB,iBAAiB,IAAI;AACnD;AACA,IAAM,oBAAoB,uBAAuB,iBAAiB,CAAC,MAAM,CAAC;AAC1E,IAAO,4BAAQ;;;ADMf,yBAA4B;AAR5B,IAAM,YAAY,CAAC,WAAW;AAS9B,IAAM,gBAAgB,oBAAoB,eAAe;AACzD,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,EACf;AACA,SAAO,eAAe,OAAO,2BAA2B,OAAO;AACjE;AACA,IAAM,iBAAiB,eAAO,oBAAY;AAAA,EACxC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE,CAAC;AAAA,EACF;AACF,MAAM;AACJ,SAAO;AAAA,IACL,YAAY,MAAM,WAAW;AAAA,IAC7B,WAAW;AAAA,EACb;AACF,CAAC;AACD,IAAM,aAAgC,iBAAW,SAASA,YAAW,SAAS,KAAK;AACjF,QAAM,QAAQ,cAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM,aAAa;AACnB,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,gBAAgB,SAAS;AAAA,IAChD,cAAc;AAAA,IACd,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,EACzC,GAAG,KAAK,CAAC;AACX,CAAC;AACD,OAAwC,WAAW,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQpF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,qBAAQ;", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "_jsx", "PropTypes"]}