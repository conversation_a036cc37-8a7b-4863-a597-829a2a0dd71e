import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>2<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>temC<PERSON>nt,
  TreeViewCollapseIcon,
  TreeViewContext,
  TreeViewExpandIcon,
  getTreeItemUtilityClass,
  treeItemClasses,
  useTreeItemState,
  useTreeViewContext
} from "./chunk-ODYFQNIF.js";
import {
  Collapse_default
} from "./chunk-JNVPO6EQ.js";
import "./chunk-TJ4WWSAQ.js";
import "./chunk-NSGBF6FD.js";
import "./chunk-QUAT4HAD.js";
import "./chunk-HZOIS4LS.js";
import "./chunk-B6VKBZX6.js";
import "./chunk-4FTWOKSW.js";
import "./chunk-NRZDFIO4.js";
import "./chunk-YVJAHDHA.js";
import "./chunk-KM4ZM5QV.js";
import "./chunk-WLH4OJGQ.js";
import "./chunk-65T5YMFT.js";
import "./chunk-P3F2W34V.js";
import "./chunk-G4V5HJJ6.js";
import "./chunk-AI4UB6XS.js";
import "./chunk-YWSF47Y5.js";
import "./chunk-2WMRNP5B.js";
import "./chunk-3H5FQ726.js";
import "./chunk-QGOONFNA.js";
import "./chunk-AOOGZM32.js";
import "./chunk-B4ZTXNQK.js";
import "./chunk-ZEHHXLMY.js";
import "./chunk-RKIOA3HS.js";
import "./chunk-FMFFUJ5P.js";
import {
  useTheme
} from "./chunk-KVEADFL7.js";
import {
  alpha,
  shouldForwardProp
} from "./chunk-FQ6I3OD7.js";
import "./chunk-SM7P2H3Y.js";
import "./chunk-LLFVOSDC.js";
import "./chunk-BXTOBC5G.js";
import "./chunk-D7ZASVPN.js";
import {
  styled_default
} from "./chunk-KIJLS2TV.js";
import {
  useThemeProps
} from "./chunk-ZYUAWKJJ.js";
import "./chunk-J4FXFBWI.js";
import "./chunk-7TAWK6YG.js";
import "./chunk-WD5ZKRWL.js";
import "./chunk-ZHHDQ2WX.js";
import "./chunk-34Q2ZYKO.js";
import "./chunk-SM3XYVA4.js";
import "./chunk-PPWWUXQB.js";
import "./chunk-MRMAHORD.js";
import "./chunk-UGF722XA.js";
import "./chunk-GNCSCC2N.js";
import "./chunk-GVLA6GQ5.js";
import "./chunk-QWWR7BFH.js";
import "./chunk-PTZEH3KO.js";
import {
  extractEventHandlers,
  resolveComponentProps,
  useSlotProps
} from "./chunk-P6LQB3KR.js";
import {
  ownerDocument,
  unsupportedProp,
  useEventCallback_default
} from "./chunk-EXKCK6CI.js";
import {
  useEnhancedEffect_default
} from "./chunk-56VEPRXN.js";
import "./chunk-XYO5E4UC.js";
import {
  useForkRef,
  useId
} from "./chunk-RCV74CT4.js";
import "./chunk-EDHI6VEC.js";
import "./chunk-3UUYZXPW.js";
import "./chunk-HTVIEQAM.js";
import {
  clsx_default
} from "./chunk-YV3COZNF.js";
import {
  composeClasses,
  generateUtilityClass,
  generateUtilityClasses
} from "./chunk-EH52VBW6.js";
import "./chunk-S3K3XWQY.js";
import {
  require_prop_types
} from "./chunk-MDE6ZET7.js";
import "./chunk-CMWVKTKB.js";
import "./chunk-OEFUZPWO.js";
import {
  _objectWithoutPropertiesLoose
} from "./chunk-OBSDRUBD.js";
import "./chunk-NZ77J7BH.js";
import {
  require_jsx_runtime
} from "./chunk-D4DBS43D.js";
import "./chunk-WCKTVIEY.js";
import "./chunk-Y2JWQXYQ.js";
import {
  _extends,
  init_extends
} from "./chunk-4GAI7T4A.js";
import "./chunk-IDEFXWKH.js";
import "./chunk-RPJ5T6V5.js";
import "./chunk-SIO7KVAJ.js";
import {
  require_react
} from "./chunk-R56R2YIZ.js";
import {
  __toESM
} from "./chunk-BYPFWIQ6.js";

// node_modules/@mui/x-tree-view/TreeView/TreeView.js
init_extends();
var React15 = __toESM(require_react());
var import_prop_types3 = __toESM(require_prop_types());

// node_modules/@mui/x-tree-view/TreeView/treeViewClasses.js
function getTreeViewUtilityClass(slot) {
  return generateUtilityClass("MuiTreeView", slot);
}
var treeViewClasses = generateUtilityClasses("MuiTreeView", ["root"]);

// node_modules/@mui/x-tree-view/SimpleTreeView/SimpleTreeView.js
init_extends();
var React14 = __toESM(require_react());
var import_prop_types2 = __toESM(require_prop_types());

// node_modules/@mui/x-tree-view/SimpleTreeView/simpleTreeViewClasses.js
function getSimpleTreeViewUtilityClass(slot) {
  return generateUtilityClass("MuiSimpleTreeView", slot);
}
var simpleTreeViewClasses = generateUtilityClasses("MuiSimpleTreeView", ["root"]);

// node_modules/@mui/x-tree-view/internals/useTreeView/useTreeView.js
init_extends();
var React3 = __toESM(require_react());

// node_modules/@mui/x-tree-view/internals/useTreeView/useTreeViewModels.js
init_extends();
var React = __toESM(require_react());
var useTreeViewModels = (plugins, props) => {
  const modelsRef = React.useRef({});
  const [modelsState, setModelsState] = React.useState(() => {
    const initialState = {};
    plugins.forEach((plugin) => {
      if (plugin.models) {
        Object.entries(plugin.models).forEach(([modelName, modelInitializer]) => {
          modelsRef.current[modelName] = {
            isControlled: props[modelName] !== void 0,
            getDefaultValue: modelInitializer.getDefaultValue
          };
          initialState[modelName] = modelInitializer.getDefaultValue(props);
        });
      }
    });
    return initialState;
  });
  const models = Object.fromEntries(Object.entries(modelsRef.current).map(([modelName, model]) => {
    const value = props[modelName] ?? modelsState[modelName];
    return [modelName, {
      value,
      setControlledValue: (newValue) => {
        if (!model.isControlled) {
          setModelsState((prevState) => _extends({}, prevState, {
            [modelName]: newValue
          }));
        }
      }
    }];
  }));
  if (true) {
    Object.entries(modelsRef.current).forEach(([modelName, model]) => {
      const controlled = props[modelName];
      const newDefaultValue = model.getDefaultValue(props);
      React.useEffect(() => {
        if (model.isControlled !== (controlled !== void 0)) {
          console.error([`MUI X: A component is changing the ${model.isControlled ? "" : "un"}controlled ${modelName} state of TreeView to be ${model.isControlled ? "un" : ""}controlled.`, "Elements should not switch from uncontrolled to controlled (or vice versa).", `Decide between using a controlled or uncontrolled ${modelName} element for the lifetime of the component.`, "The nature of the state is determined during the first render. It's considered controlled if the value is not `undefined`.", "More info: https://fb.me/react-controlled-components"].join("\n"));
        }
      }, [controlled]);
      const {
        current: defaultValue
      } = React.useRef(newDefaultValue);
      React.useEffect(() => {
        if (!model.isControlled && defaultValue !== newDefaultValue) {
          console.error([`MUI X: A component is changing the default ${modelName} state of an uncontrolled TreeView after being initialized. To suppress this warning opt to use a controlled TreeView.`].join("\n"));
        }
      }, [JSON.stringify(newDefaultValue)]);
    });
  }
  return models;
};

// node_modules/@mui/x-tree-view/internals/corePlugins/useTreeViewInstanceEvents/useTreeViewInstanceEvents.js
var React2 = __toESM(require_react());

// node_modules/@mui/x-tree-view/internals/utils/EventManager.js
var EventManager = class {
  constructor() {
    this.maxListeners = 20;
    this.warnOnce = false;
    this.events = {};
  }
  on(eventName, listener, options = {}) {
    let collection = this.events[eventName];
    if (!collection) {
      collection = {
        highPriority: /* @__PURE__ */ new Map(),
        regular: /* @__PURE__ */ new Map()
      };
      this.events[eventName] = collection;
    }
    if (options.isFirst) {
      collection.highPriority.set(listener, true);
    } else {
      collection.regular.set(listener, true);
    }
    if (true) {
      const collectionSize = collection.highPriority.size + collection.regular.size;
      if (collectionSize > this.maxListeners && !this.warnOnce) {
        this.warnOnce = true;
        console.warn([`Possible EventEmitter memory leak detected. ${collectionSize} ${eventName} listeners added.`].join("\n"));
      }
    }
  }
  removeListener(eventName, listener) {
    if (this.events[eventName]) {
      this.events[eventName].regular.delete(listener);
      this.events[eventName].highPriority.delete(listener);
    }
  }
  removeAllListeners() {
    this.events = {};
  }
  emit(eventName, ...args) {
    const collection = this.events[eventName];
    if (!collection) {
      return;
    }
    const highPriorityListeners = Array.from(collection.highPriority.keys());
    const regularListeners = Array.from(collection.regular.keys());
    for (let i = highPriorityListeners.length - 1; i >= 0; i -= 1) {
      const listener = highPriorityListeners[i];
      if (collection.highPriority.has(listener)) {
        listener.apply(this, args);
      }
    }
    for (let i = 0; i < regularListeners.length; i += 1) {
      const listener = regularListeners[i];
      if (collection.regular.has(listener)) {
        listener.apply(this, args);
      }
    }
  }
  once(eventName, listener) {
    const that = this;
    this.on(eventName, function oneTimeListener(...args) {
      that.removeListener(eventName, oneTimeListener);
      listener.apply(that, args);
    });
  }
};

// node_modules/@mui/x-tree-view/internals/corePlugins/useTreeViewInstanceEvents/useTreeViewInstanceEvents.js
var isSyntheticEvent = (event) => {
  return event.isPropagationStopped !== void 0;
};
var useTreeViewInstanceEvents = () => {
  const [eventManager] = React2.useState(() => new EventManager());
  const publishEvent = React2.useCallback((...args) => {
    const [name, params, event = {}] = args;
    event.defaultMuiPrevented = false;
    if (isSyntheticEvent(event) && event.isPropagationStopped()) {
      return;
    }
    eventManager.emit(name, params, event);
  }, [eventManager]);
  const subscribeEvent = React2.useCallback((event, handler) => {
    eventManager.on(event, handler);
    return () => {
      eventManager.removeListener(event, handler);
    };
  }, [eventManager]);
  return {
    instance: {
      $$publishEvent: publishEvent,
      $$subscribeEvent: subscribeEvent
    }
  };
};
useTreeViewInstanceEvents.params = {};

// node_modules/@mui/x-tree-view/internals/corePlugins/corePlugins.js
var TREE_VIEW_CORE_PLUGINS = [useTreeViewInstanceEvents];

// node_modules/@mui/x-tree-view/internals/useTreeView/useTreeView.js
function useTreeViewApiInitialization(inputApiRef) {
  const fallbackPublicApiRef = React3.useRef({});
  if (inputApiRef) {
    if (inputApiRef.current == null) {
      inputApiRef.current = {};
    }
    return inputApiRef.current;
  }
  return fallbackPublicApiRef.current;
}
var useTreeView = (inParams) => {
  const plugins = [...TREE_VIEW_CORE_PLUGINS, ...inParams.plugins];
  const params = plugins.reduce((acc, plugin) => {
    if (plugin.getDefaultizedParams) {
      return plugin.getDefaultizedParams(acc);
    }
    return acc;
  }, inParams);
  const models = useTreeViewModels(plugins, params);
  const instanceRef = React3.useRef({});
  const instance = instanceRef.current;
  const publicAPI = useTreeViewApiInitialization(inParams.apiRef);
  const innerRootRef = React3.useRef(null);
  const handleRootRef = useForkRef(innerRootRef, inParams.rootRef);
  const [state, setState] = React3.useState(() => {
    const temp = {};
    plugins.forEach((plugin) => {
      if (plugin.getInitialState) {
        Object.assign(temp, plugin.getInitialState(params));
      }
    });
    return temp;
  });
  const rootPropsGetters = [];
  const contextValue = {
    publicAPI,
    instance,
    rootRef: innerRootRef
  };
  const runPlugin = (plugin) => {
    const pluginResponse = plugin({
      instance,
      params,
      slots: params.slots,
      slotProps: params.slotProps,
      state,
      setState,
      rootRef: innerRootRef,
      models
    });
    if (pluginResponse.getRootProps) {
      rootPropsGetters.push(pluginResponse.getRootProps);
    }
    if (pluginResponse.publicAPI) {
      Object.assign(publicAPI, pluginResponse.publicAPI);
    }
    if (pluginResponse.instance) {
      Object.assign(instance, pluginResponse.instance);
    }
    if (pluginResponse.contextValue) {
      Object.assign(contextValue, pluginResponse.contextValue);
    }
  };
  plugins.forEach(runPlugin);
  contextValue.runItemPlugins = (itemPluginProps) => {
    let finalRootRef = null;
    let finalContentRef = null;
    plugins.forEach((plugin) => {
      if (!plugin.itemPlugin) {
        return;
      }
      const itemPluginResponse = plugin.itemPlugin({
        props: itemPluginProps,
        rootRef: finalRootRef,
        contentRef: finalContentRef
      });
      if (itemPluginResponse == null ? void 0 : itemPluginResponse.rootRef) {
        finalRootRef = itemPluginResponse.rootRef;
      }
      if (itemPluginResponse == null ? void 0 : itemPluginResponse.contentRef) {
        finalContentRef = itemPluginResponse.contentRef;
      }
    });
    return {
      contentRef: finalContentRef,
      rootRef: finalRootRef
    };
  };
  const itemWrappers = plugins.map((plugin) => plugin.wrapItem).filter((wrapItem) => !!wrapItem);
  contextValue.wrapItem = ({
    itemId,
    children
  }) => {
    let finalChildren = children;
    itemWrappers.forEach((itemWrapper) => {
      finalChildren = itemWrapper({
        itemId,
        children: finalChildren
      });
    });
    return finalChildren;
  };
  const rootWrappers = plugins.map((plugin) => plugin.wrapRoot).filter((wrapRoot) => !!wrapRoot);
  contextValue.wrapRoot = ({
    children
  }) => {
    let finalChildren = children;
    rootWrappers.forEach((rootWrapper) => {
      finalChildren = rootWrapper({
        children: finalChildren
      });
    });
    return finalChildren;
  };
  const getRootProps = (otherHandlers = {}) => {
    const rootProps = _extends({
      role: "tree"
    }, otherHandlers, {
      ref: handleRootRef
    });
    rootPropsGetters.forEach((rootPropsGetter) => {
      Object.assign(rootProps, rootPropsGetter(otherHandlers));
    });
    return rootProps;
  };
  return {
    getRootProps,
    rootRef: handleRootRef,
    contextValue,
    instance
  };
};

// node_modules/@mui/x-tree-view/internals/TreeViewProvider/TreeViewProvider.js
var React4 = __toESM(require_react());
var import_jsx_runtime = __toESM(require_jsx_runtime());
function TreeViewProvider(props) {
  const {
    value,
    children
  } = props;
  return (0, import_jsx_runtime.jsx)(TreeViewContext.Provider, {
    value,
    children: value.wrapRoot({
      children
    })
  });
}

// node_modules/@mui/x-tree-view/internals/plugins/useTreeViewId/useTreeViewId.js
var React5 = __toESM(require_react());
var useTreeViewId = ({
  params
}) => {
  const treeId = useId(params.id);
  const getTreeItemIdAttribute = React5.useCallback((itemId, idAttribute) => idAttribute ?? `${treeId}-${itemId}`, [treeId]);
  return {
    getRootProps: () => ({
      id: treeId
    }),
    instance: {
      getTreeItemIdAttribute
    }
  };
};
useTreeViewId.params = {
  id: true
};

// node_modules/@mui/x-tree-view/internals/plugins/useTreeViewItems/useTreeViewItems.js
init_extends();
var React6 = __toESM(require_react());

// node_modules/@mui/x-tree-view/internals/utils/publishTreeViewEvent.js
var publishTreeViewEvent = (instance, eventName, params) => {
  instance.$$publishEvent(eventName, params);
};

// node_modules/@mui/x-tree-view/internals/plugins/useTreeViewItems/useTreeViewItems.utils.js
var TREE_VIEW_ROOT_PARENT_ID = "__TREE_VIEW_ROOT_PARENT_ID__";
var buildSiblingIndexes = (siblings) => {
  const siblingsIndexLookup = {};
  siblings.forEach((childId, index) => {
    siblingsIndexLookup[childId] = index;
  });
  return siblingsIndexLookup;
};

// node_modules/@mui/x-tree-view/internals/plugins/useTreeViewItems/useTreeViewItems.js
var updateItemsState = ({
  items,
  isItemDisabled,
  getItemLabel,
  getItemId
}) => {
  const itemMetaMap = {};
  const itemMap = {};
  const itemOrderedChildrenIds = {
    [TREE_VIEW_ROOT_PARENT_ID]: []
  };
  const processItem = (item, parentId) => {
    var _a, _b;
    const id = getItemId ? getItemId(item) : item.id;
    if (id == null) {
      throw new Error(["MUI X: The Tree View component requires all items to have a unique `id` property.", "Alternatively, you can use the `getItemId` prop to specify a custom id for each item.", "An item was provided without id in the `items` prop:", JSON.stringify(item)].join("\n"));
    }
    if (itemMetaMap[id] != null) {
      throw new Error(["MUI X: The Tree View component requires all items to have a unique `id` property.", "Alternatively, you can use the `getItemId` prop to specify a custom id for each item.", `Two items were provided with the same id in the \`items\` prop: "${id}"`].join("\n"));
    }
    const label = getItemLabel ? getItemLabel(item) : item.label;
    if (label == null) {
      throw new Error(["MUI X: The Tree View component requires all items to have a `label` property.", "Alternatively, you can use the `getItemLabel` prop to specify a custom label for each item.", "An item was provided without label in the `items` prop:", JSON.stringify(item)].join("\n"));
    }
    itemMetaMap[id] = {
      id,
      label,
      parentId,
      idAttribute: void 0,
      expandable: !!((_a = item.children) == null ? void 0 : _a.length),
      disabled: isItemDisabled ? isItemDisabled(item) : false
    };
    itemMap[id] = item;
    itemOrderedChildrenIds[id] = [];
    const parentIdWithDefault = parentId ?? TREE_VIEW_ROOT_PARENT_ID;
    if (!itemOrderedChildrenIds[parentIdWithDefault]) {
      itemOrderedChildrenIds[parentIdWithDefault] = [];
    }
    itemOrderedChildrenIds[parentIdWithDefault].push(id);
    (_b = item.children) == null ? void 0 : _b.forEach((child) => processItem(child, id));
  };
  items.forEach((item) => processItem(item, null));
  const itemChildrenIndexes = {};
  Object.keys(itemOrderedChildrenIds).forEach((parentId) => {
    itemChildrenIndexes[parentId] = buildSiblingIndexes(itemOrderedChildrenIds[parentId]);
  });
  return {
    itemMetaMap,
    itemMap,
    itemOrderedChildrenIds,
    itemChildrenIndexes
  };
};
var useTreeViewItems = ({
  instance,
  params,
  state,
  setState
}) => {
  const getItemMeta = React6.useCallback((itemId) => state.items.itemMetaMap[itemId], [state.items.itemMetaMap]);
  const getItem = React6.useCallback((itemId) => state.items.itemMap[itemId], [state.items.itemMap]);
  const isItemDisabled = React6.useCallback((itemId) => {
    if (itemId == null) {
      return false;
    }
    let itemMeta = instance.getItemMeta(itemId);
    if (!itemMeta) {
      return false;
    }
    if (itemMeta.disabled) {
      return true;
    }
    while (itemMeta.parentId != null) {
      itemMeta = instance.getItemMeta(itemMeta.parentId);
      if (itemMeta.disabled) {
        return true;
      }
    }
    return false;
  }, [instance]);
  const getItemIndex = React6.useCallback((itemId) => {
    const parentId = instance.getItemMeta(itemId).parentId ?? TREE_VIEW_ROOT_PARENT_ID;
    return state.items.itemChildrenIndexes[parentId][itemId];
  }, [instance, state.items.itemChildrenIndexes]);
  const getItemOrderedChildrenIds = React6.useCallback((itemId) => state.items.itemOrderedChildrenIds[itemId ?? TREE_VIEW_ROOT_PARENT_ID] ?? [], [state.items.itemOrderedChildrenIds]);
  const isItemNavigable = (itemId) => {
    if (params.disabledItemsFocusable) {
      return true;
    }
    return !instance.isItemDisabled(itemId);
  };
  const areItemUpdatesPreventedRef = React6.useRef(false);
  const preventItemUpdates = React6.useCallback(() => {
    areItemUpdatesPreventedRef.current = true;
  }, []);
  const areItemUpdatesPrevented = React6.useCallback(() => areItemUpdatesPreventedRef.current, []);
  React6.useEffect(() => {
    if (instance.areItemUpdatesPrevented()) {
      return;
    }
    setState((prevState) => {
      const newState = updateItemsState({
        items: params.items,
        isItemDisabled: params.isItemDisabled,
        getItemId: params.getItemId,
        getItemLabel: params.getItemLabel
      });
      Object.values(prevState.items.itemMetaMap).forEach((item) => {
        if (!newState.itemMetaMap[item.id]) {
          publishTreeViewEvent(instance, "removeItem", {
            id: item.id
          });
        }
      });
      return _extends({}, prevState, {
        items: newState
      });
    });
  }, [instance, setState, params.items, params.isItemDisabled, params.getItemId, params.getItemLabel]);
  const getItemsToRender = () => {
    const getPropsFromItemId = (id) => {
      const item = state.items.itemMetaMap[id];
      return {
        label: item.label,
        itemId: item.id,
        id: item.idAttribute,
        children: state.items.itemOrderedChildrenIds[id].map(getPropsFromItemId)
      };
    };
    return state.items.itemOrderedChildrenIds[TREE_VIEW_ROOT_PARENT_ID].map(getPropsFromItemId);
  };
  return {
    publicAPI: {
      getItem
    },
    instance: {
      getItemMeta,
      getItem,
      getItemsToRender,
      getItemIndex,
      getItemOrderedChildrenIds,
      isItemDisabled,
      isItemNavigable,
      preventItemUpdates,
      areItemUpdatesPrevented
    },
    contextValue: {
      disabledItemsFocusable: params.disabledItemsFocusable
    }
  };
};
useTreeViewItems.getInitialState = (params) => ({
  items: updateItemsState({
    items: params.items,
    isItemDisabled: params.isItemDisabled,
    getItemId: params.getItemId,
    getItemLabel: params.getItemLabel
  })
});
useTreeViewItems.getDefaultizedParams = (params) => _extends({}, params, {
  disabledItemsFocusable: params.disabledItemsFocusable ?? false
});
useTreeViewItems.params = {
  disabledItemsFocusable: true,
  items: true,
  isItemDisabled: true,
  getItemLabel: true,
  getItemId: true
};

// node_modules/@mui/x-tree-view/internals/plugins/useTreeViewExpansion/useTreeViewExpansion.js
init_extends();
var React7 = __toESM(require_react());
var useTreeViewExpansion = ({
  instance,
  params,
  models
}) => {
  const expandedItemsMap = React7.useMemo(() => {
    const temp = /* @__PURE__ */ new Map();
    models.expandedItems.value.forEach((id) => {
      temp.set(id, true);
    });
    return temp;
  }, [models.expandedItems.value]);
  const setExpandedItems = (event, value) => {
    var _a;
    (_a = params.onExpandedItemsChange) == null ? void 0 : _a.call(params, event, value);
    models.expandedItems.setControlledValue(value);
  };
  const isItemExpanded = React7.useCallback((itemId) => expandedItemsMap.has(itemId), [expandedItemsMap]);
  const isItemExpandable2 = React7.useCallback((itemId) => {
    var _a;
    return !!((_a = instance.getItemMeta(itemId)) == null ? void 0 : _a.expandable);
  }, [instance]);
  const toggleItemExpansion = useEventCallback_default((event, itemId) => {
    const isExpandedBefore = instance.isItemExpanded(itemId);
    instance.setItemExpansion(event, itemId, !isExpandedBefore);
  });
  const setItemExpansion = useEventCallback_default((event, itemId, isExpanded) => {
    const isExpandedBefore = instance.isItemExpanded(itemId);
    if (isExpandedBefore === isExpanded) {
      return;
    }
    let newExpanded;
    if (isExpanded) {
      newExpanded = [itemId].concat(models.expandedItems.value);
    } else {
      newExpanded = models.expandedItems.value.filter((id) => id !== itemId);
    }
    if (params.onItemExpansionToggle) {
      params.onItemExpansionToggle(event, itemId, isExpanded);
    }
    setExpandedItems(event, newExpanded);
  });
  const expandAllSiblings = (event, itemId) => {
    const itemMeta = instance.getItemMeta(itemId);
    const siblings = instance.getItemOrderedChildrenIds(itemMeta.parentId);
    const diff = siblings.filter((child) => instance.isItemExpandable(child) && !instance.isItemExpanded(child));
    const newExpanded = models.expandedItems.value.concat(diff);
    if (diff.length > 0) {
      if (params.onItemExpansionToggle) {
        diff.forEach((newlyExpandedItemId) => {
          params.onItemExpansionToggle(event, newlyExpandedItemId, true);
        });
      }
      setExpandedItems(event, newExpanded);
    }
  };
  return {
    publicAPI: {
      setItemExpansion
    },
    instance: {
      isItemExpanded,
      isItemExpandable: isItemExpandable2,
      setItemExpansion,
      toggleItemExpansion,
      expandAllSiblings
    }
  };
};
useTreeViewExpansion.models = {
  expandedItems: {
    getDefaultValue: (params) => params.defaultExpandedItems
  }
};
var DEFAULT_EXPANDED_ITEMS = [];
useTreeViewExpansion.getDefaultizedParams = (params) => _extends({}, params, {
  defaultExpandedItems: params.defaultExpandedItems ?? DEFAULT_EXPANDED_ITEMS
});
useTreeViewExpansion.params = {
  expandedItems: true,
  defaultExpandedItems: true,
  onExpandedItemsChange: true,
  onItemExpansionToggle: true
};

// node_modules/@mui/x-tree-view/internals/plugins/useTreeViewSelection/useTreeViewSelection.js
init_extends();
var React8 = __toESM(require_react());

// node_modules/@mui/x-tree-view/internals/utils/tree.js
var getLastNavigableItemInArray = (instance, items) => {
  let itemIndex = items.length - 1;
  while (itemIndex >= 0 && !instance.isItemNavigable(items[itemIndex])) {
    itemIndex -= 1;
  }
  if (itemIndex === -1) {
    return void 0;
  }
  return items[itemIndex];
};
var getPreviousNavigableItem = (instance, itemId) => {
  const itemMeta = instance.getItemMeta(itemId);
  const siblings = instance.getItemOrderedChildrenIds(itemMeta.parentId);
  const itemIndex = instance.getItemIndex(itemId);
  if (itemIndex === 0) {
    return itemMeta.parentId;
  }
  let currentItemId = siblings[itemIndex - 1];
  let lastNavigableChild = getLastNavigableItemInArray(instance, instance.getItemOrderedChildrenIds(currentItemId));
  while (instance.isItemExpanded(currentItemId) && lastNavigableChild != null) {
    currentItemId = lastNavigableChild;
    lastNavigableChild = instance.getItemOrderedChildrenIds(currentItemId).find(instance.isItemNavigable);
  }
  return currentItemId;
};
var getNextNavigableItem = (instance, itemId) => {
  if (instance.isItemExpanded(itemId)) {
    const firstNavigableChild = instance.getItemOrderedChildrenIds(itemId).find(instance.isItemNavigable);
    if (firstNavigableChild != null) {
      return firstNavigableChild;
    }
  }
  let itemMeta = instance.getItemMeta(itemId);
  while (itemMeta != null) {
    const siblings = instance.getItemOrderedChildrenIds(itemMeta.parentId);
    const currentItemIndex = instance.getItemIndex(itemMeta.id);
    if (currentItemIndex < siblings.length - 1) {
      let nextItemIndex = currentItemIndex + 1;
      while (!instance.isItemNavigable(siblings[nextItemIndex]) && nextItemIndex < siblings.length - 1) {
        nextItemIndex += 1;
      }
      if (instance.isItemNavigable(siblings[nextItemIndex])) {
        return siblings[nextItemIndex];
      }
    }
    itemMeta = instance.getItemMeta(itemMeta.parentId);
  }
  return null;
};
var getLastNavigableItem = (instance) => {
  let itemId = null;
  while (itemId == null || instance.isItemExpanded(itemId)) {
    const children = instance.getItemOrderedChildrenIds(itemId);
    const lastNavigableChild = getLastNavigableItemInArray(instance, children);
    if (lastNavigableChild == null) {
      return itemId;
    }
    itemId = lastNavigableChild;
  }
  return itemId;
};
var getFirstNavigableItem = (instance) => instance.getItemOrderedChildrenIds(null).find(instance.isItemNavigable);
var findOrderInTremauxTree = (instance, itemAId, itemBId) => {
  if (itemAId === itemBId) {
    return [itemAId, itemBId];
  }
  const itemMetaA = instance.getItemMeta(itemAId);
  const itemMetaB = instance.getItemMeta(itemBId);
  if (itemMetaA.parentId === itemMetaB.id || itemMetaB.parentId === itemMetaA.id) {
    return itemMetaB.parentId === itemMetaA.id ? [itemMetaA.id, itemMetaB.id] : [itemMetaB.id, itemMetaA.id];
  }
  const aFamily = [itemMetaA.id];
  const bFamily = [itemMetaB.id];
  let aAncestor = itemMetaA.parentId;
  let bAncestor = itemMetaB.parentId;
  let aAncestorIsCommon = bFamily.indexOf(aAncestor) !== -1;
  let bAncestorIsCommon = aFamily.indexOf(bAncestor) !== -1;
  let continueA = true;
  let continueB = true;
  while (!bAncestorIsCommon && !aAncestorIsCommon) {
    if (continueA) {
      aFamily.push(aAncestor);
      aAncestorIsCommon = bFamily.indexOf(aAncestor) !== -1;
      continueA = aAncestor !== null;
      if (!aAncestorIsCommon && continueA) {
        aAncestor = instance.getItemMeta(aAncestor).parentId;
      }
    }
    if (continueB && !aAncestorIsCommon) {
      bFamily.push(bAncestor);
      bAncestorIsCommon = aFamily.indexOf(bAncestor) !== -1;
      continueB = bAncestor !== null;
      if (!bAncestorIsCommon && continueB) {
        bAncestor = instance.getItemMeta(bAncestor).parentId;
      }
    }
  }
  const commonAncestor = aAncestorIsCommon ? aAncestor : bAncestor;
  const ancestorFamily = instance.getItemOrderedChildrenIds(commonAncestor);
  const aSide = aFamily[aFamily.indexOf(commonAncestor) - 1];
  const bSide = bFamily[bFamily.indexOf(commonAncestor) - 1];
  return ancestorFamily.indexOf(aSide) < ancestorFamily.indexOf(bSide) ? [itemAId, itemBId] : [itemBId, itemAId];
};
var getNavigableItemsInRange = (instance, itemAId, itemBId) => {
  const [first, last] = findOrderInTremauxTree(instance, itemAId, itemBId);
  const items = [first];
  let current = first;
  while (current !== last) {
    current = getNextNavigableItem(instance, current);
    items.push(current);
  }
  return items;
};

// node_modules/@mui/x-tree-view/internals/plugins/useTreeViewSelection/useTreeViewSelection.js
var useTreeViewSelection = ({
  instance,
  params,
  models
}) => {
  const lastSelectedItem = React8.useRef(null);
  const lastSelectionWasRange = React8.useRef(false);
  const currentRangeSelection = React8.useRef([]);
  const setSelectedItems = (event, newSelectedItems) => {
    if (params.onItemSelectionToggle) {
      if (params.multiSelect) {
        const addedItems = newSelectedItems.filter((itemId) => !instance.isItemSelected(itemId));
        const removedItems = models.selectedItems.value.filter((itemId) => !newSelectedItems.includes(itemId));
        addedItems.forEach((itemId) => {
          params.onItemSelectionToggle(event, itemId, true);
        });
        removedItems.forEach((itemId) => {
          params.onItemSelectionToggle(event, itemId, false);
        });
      } else if (newSelectedItems !== models.selectedItems.value) {
        if (models.selectedItems.value != null) {
          params.onItemSelectionToggle(event, models.selectedItems.value, false);
        }
        if (newSelectedItems != null) {
          params.onItemSelectionToggle(event, newSelectedItems, true);
        }
      }
    }
    if (params.onSelectedItemsChange) {
      params.onSelectedItemsChange(event, newSelectedItems);
    }
    models.selectedItems.setControlledValue(newSelectedItems);
  };
  const isItemSelected = (itemId) => Array.isArray(models.selectedItems.value) ? models.selectedItems.value.indexOf(itemId) !== -1 : models.selectedItems.value === itemId;
  const selectItem = (event, itemId, multiple = false) => {
    if (params.disableSelection) {
      return;
    }
    if (multiple) {
      if (Array.isArray(models.selectedItems.value)) {
        let newSelected;
        if (models.selectedItems.value.indexOf(itemId) !== -1) {
          newSelected = models.selectedItems.value.filter((id) => id !== itemId);
        } else {
          newSelected = [itemId].concat(models.selectedItems.value);
        }
        setSelectedItems(event, newSelected);
      }
    } else {
      const newSelected = params.multiSelect ? [itemId] : itemId;
      setSelectedItems(event, newSelected);
    }
    lastSelectedItem.current = itemId;
    lastSelectionWasRange.current = false;
    currentRangeSelection.current = [];
  };
  const handleRangeArrowSelect = (event, items) => {
    let base = models.selectedItems.value.slice();
    const {
      start,
      next,
      current
    } = items;
    if (!next || !current) {
      return;
    }
    if (currentRangeSelection.current.indexOf(current) === -1) {
      currentRangeSelection.current = [];
    }
    if (lastSelectionWasRange.current) {
      if (currentRangeSelection.current.indexOf(next) !== -1) {
        base = base.filter((id) => id === start || id !== current);
        currentRangeSelection.current = currentRangeSelection.current.filter((id) => id === start || id !== current);
      } else {
        base.push(next);
        currentRangeSelection.current.push(next);
      }
    } else {
      base.push(next);
      currentRangeSelection.current.push(current, next);
    }
    setSelectedItems(event, base);
  };
  const handleRangeSelect = (event, items) => {
    let base = models.selectedItems.value.slice();
    const {
      start,
      end
    } = items;
    if (lastSelectionWasRange.current) {
      base = base.filter((id) => currentRangeSelection.current.indexOf(id) === -1);
    }
    let range = getNavigableItemsInRange(instance, start, end);
    range = range.filter((item) => !instance.isItemDisabled(item));
    currentRangeSelection.current = range;
    let newSelected = base.concat(range);
    newSelected = newSelected.filter((id, i) => newSelected.indexOf(id) === i);
    setSelectedItems(event, newSelected);
  };
  const selectRange = (event, items, stacked = false) => {
    if (params.disableSelection) {
      return;
    }
    const {
      start = lastSelectedItem.current,
      end,
      current
    } = items;
    if (stacked) {
      handleRangeArrowSelect(event, {
        start,
        next: end,
        current
      });
    } else if (start != null && end != null) {
      handleRangeSelect(event, {
        start,
        end
      });
    }
    lastSelectionWasRange.current = true;
  };
  const rangeSelectToFirst = (event, itemId) => {
    if (!lastSelectedItem.current) {
      lastSelectedItem.current = itemId;
    }
    const start = lastSelectionWasRange.current ? lastSelectedItem.current : itemId;
    instance.selectRange(event, {
      start,
      end: getFirstNavigableItem(instance)
    });
  };
  const rangeSelectToLast = (event, itemId) => {
    if (!lastSelectedItem.current) {
      lastSelectedItem.current = itemId;
    }
    const start = lastSelectionWasRange.current ? lastSelectedItem.current : itemId;
    instance.selectRange(event, {
      start,
      end: getLastNavigableItem(instance)
    });
  };
  return {
    getRootProps: () => ({
      "aria-multiselectable": params.multiSelect
    }),
    instance: {
      isItemSelected,
      selectItem,
      selectRange,
      rangeSelectToLast,
      rangeSelectToFirst
    },
    contextValue: {
      selection: {
        multiSelect: params.multiSelect
      }
    }
  };
};
useTreeViewSelection.models = {
  selectedItems: {
    getDefaultValue: (params) => params.defaultSelectedItems
  }
};
var DEFAULT_SELECTED_ITEMS = [];
useTreeViewSelection.getDefaultizedParams = (params) => _extends({}, params, {
  disableSelection: params.disableSelection ?? false,
  multiSelect: params.multiSelect ?? false,
  defaultSelectedItems: params.defaultSelectedItems ?? (params.multiSelect ? DEFAULT_SELECTED_ITEMS : null)
});
useTreeViewSelection.params = {
  disableSelection: true,
  multiSelect: true,
  defaultSelectedItems: true,
  selectedItems: true,
  onSelectedItemsChange: true,
  onItemSelectionToggle: true
};

// node_modules/@mui/x-tree-view/internals/plugins/useTreeViewFocus/useTreeViewFocus.js
init_extends();
var React10 = __toESM(require_react());

// node_modules/@mui/x-tree-view/internals/hooks/useInstanceEventHandler.js
var React9 = __toESM(require_react());

// node_modules/@mui/x-tree-view/internals/utils/cleanupTracking/TimerBasedCleanupTracking.js
var CLEANUP_TIMER_LOOP_MILLIS = 1e3;
var TimerBasedCleanupTracking = class {
  constructor(timeout = CLEANUP_TIMER_LOOP_MILLIS) {
    this.timeouts = /* @__PURE__ */ new Map();
    this.cleanupTimeout = CLEANUP_TIMER_LOOP_MILLIS;
    this.cleanupTimeout = timeout;
  }
  register(object, unsubscribe, unregisterToken) {
    if (!this.timeouts) {
      this.timeouts = /* @__PURE__ */ new Map();
    }
    const timeout = setTimeout(() => {
      if (typeof unsubscribe === "function") {
        unsubscribe();
      }
      this.timeouts.delete(unregisterToken.cleanupToken);
    }, this.cleanupTimeout);
    this.timeouts.set(unregisterToken.cleanupToken, timeout);
  }
  unregister(unregisterToken) {
    const timeout = this.timeouts.get(unregisterToken.cleanupToken);
    if (timeout) {
      this.timeouts.delete(unregisterToken.cleanupToken);
      clearTimeout(timeout);
    }
  }
  reset() {
    if (this.timeouts) {
      this.timeouts.forEach((value, key) => {
        this.unregister({
          cleanupToken: key
        });
      });
      this.timeouts = void 0;
    }
  }
};

// node_modules/@mui/x-tree-view/internals/utils/cleanupTracking/FinalizationRegistryBasedCleanupTracking.js
var FinalizationRegistryBasedCleanupTracking = class {
  constructor() {
    this.registry = new FinalizationRegistry((unsubscribe) => {
      if (typeof unsubscribe === "function") {
        unsubscribe();
      }
    });
  }
  register(object, unsubscribe, unregisterToken) {
    this.registry.register(object, unsubscribe, unregisterToken);
  }
  unregister(unregisterToken) {
    this.registry.unregister(unregisterToken);
  }
  // eslint-disable-next-line class-methods-use-this
  reset() {
  }
};

// node_modules/@mui/x-tree-view/internals/hooks/useInstanceEventHandler.js
var ObjectToBeRetainedByReact = class {
};
function createUseInstanceEventHandler(registryContainer2) {
  let cleanupTokensCounter = 0;
  return function useInstanceEventHandler2(instance, eventName, handler) {
    if (registryContainer2.registry === null) {
      registryContainer2.registry = typeof FinalizationRegistry !== "undefined" ? new FinalizationRegistryBasedCleanupTracking() : new TimerBasedCleanupTracking();
    }
    const [objectRetainedByReact] = React9.useState(new ObjectToBeRetainedByReact());
    const subscription = React9.useRef(null);
    const handlerRef = React9.useRef();
    handlerRef.current = handler;
    const cleanupTokenRef = React9.useRef(null);
    if (!subscription.current && handlerRef.current) {
      const enhancedHandler = (params, event) => {
        var _a;
        if (!event.defaultMuiPrevented) {
          (_a = handlerRef.current) == null ? void 0 : _a.call(handlerRef, params, event);
        }
      };
      subscription.current = instance.$$subscribeEvent(eventName, enhancedHandler);
      cleanupTokensCounter += 1;
      cleanupTokenRef.current = {
        cleanupToken: cleanupTokensCounter
      };
      registryContainer2.registry.register(
        objectRetainedByReact,
        // The callback below will be called once this reference stops being retained
        () => {
          var _a;
          (_a = subscription.current) == null ? void 0 : _a.call(subscription);
          subscription.current = null;
          cleanupTokenRef.current = null;
        },
        cleanupTokenRef.current
      );
    } else if (!handlerRef.current && subscription.current) {
      subscription.current();
      subscription.current = null;
      if (cleanupTokenRef.current) {
        registryContainer2.registry.unregister(cleanupTokenRef.current);
        cleanupTokenRef.current = null;
      }
    }
    React9.useEffect(() => {
      if (!subscription.current && handlerRef.current) {
        const enhancedHandler = (params, event) => {
          var _a;
          if (!event.defaultMuiPrevented) {
            (_a = handlerRef.current) == null ? void 0 : _a.call(handlerRef, params, event);
          }
        };
        subscription.current = instance.$$subscribeEvent(eventName, enhancedHandler);
      }
      if (cleanupTokenRef.current && registryContainer2.registry) {
        registryContainer2.registry.unregister(cleanupTokenRef.current);
        cleanupTokenRef.current = null;
      }
      return () => {
        var _a;
        (_a = subscription.current) == null ? void 0 : _a.call(subscription);
        subscription.current = null;
      };
    }, [instance, eventName]);
  };
}
var registryContainer = {
  registry: null
};
var unstable_resetCleanupTracking = () => {
  var _a;
  (_a = registryContainer.registry) == null ? void 0 : _a.reset();
  registryContainer.registry = null;
};
var useInstanceEventHandler = createUseInstanceEventHandler(registryContainer);

// node_modules/@mui/x-tree-view/internals/utils/utils.js
var getActiveElement = (root = document) => {
  const activeEl = root.activeElement;
  if (!activeEl) {
    return null;
  }
  if (activeEl.shadowRoot) {
    return getActiveElement(activeEl.shadowRoot);
  }
  return activeEl;
};

// node_modules/@mui/x-tree-view/internals/plugins/useTreeViewFocus/useTreeViewFocus.js
var useTabbableItemId = (instance, selectedItems) => {
  const isItemVisible = (itemId) => {
    const itemMeta = instance.getItemMeta(itemId);
    return itemMeta && (itemMeta.parentId == null || instance.isItemExpanded(itemMeta.parentId));
  };
  let tabbableItemId;
  if (Array.isArray(selectedItems)) {
    tabbableItemId = selectedItems.find(isItemVisible);
  } else if (selectedItems != null && isItemVisible(selectedItems)) {
    tabbableItemId = selectedItems;
  }
  if (tabbableItemId == null) {
    tabbableItemId = getFirstNavigableItem(instance);
  }
  return tabbableItemId;
};
var useTreeViewFocus = ({
  instance,
  params,
  state,
  setState,
  models,
  rootRef
}) => {
  const tabbableItemId = useTabbableItemId(instance, models.selectedItems.value);
  const setFocusedItemId = useEventCallback_default((itemId) => {
    const cleanItemId = typeof itemId === "function" ? itemId(state.focusedItemId) : itemId;
    if (state.focusedItemId !== cleanItemId) {
      setState((prevState) => _extends({}, prevState, {
        focusedItemId: cleanItemId
      }));
    }
  });
  const isTreeViewFocused = React10.useCallback(() => !!rootRef.current && rootRef.current.contains(getActiveElement(ownerDocument(rootRef.current))), [rootRef]);
  const isItemFocused = React10.useCallback((itemId) => state.focusedItemId === itemId && isTreeViewFocused(), [state.focusedItemId, isTreeViewFocused]);
  const isItemVisible = (itemId) => {
    const itemMeta = instance.getItemMeta(itemId);
    return itemMeta && (itemMeta.parentId == null || instance.isItemExpanded(itemMeta.parentId));
  };
  const innerFocusItem = (event, itemId) => {
    const itemMeta = instance.getItemMeta(itemId);
    const itemElement = document.getElementById(instance.getTreeItemIdAttribute(itemId, itemMeta.idAttribute));
    if (itemElement) {
      itemElement.focus();
    }
    setFocusedItemId(itemId);
    if (params.onItemFocus) {
      params.onItemFocus(event, itemId);
    }
  };
  const focusItem = useEventCallback_default((event, itemId) => {
    if (isItemVisible(itemId)) {
      innerFocusItem(event, itemId);
    }
  });
  const focusDefaultItem = useEventCallback_default((event) => {
    let itemToFocusId;
    if (Array.isArray(models.selectedItems.value)) {
      itemToFocusId = models.selectedItems.value.find(isItemVisible);
    } else if (models.selectedItems.value != null && isItemVisible(models.selectedItems.value)) {
      itemToFocusId = models.selectedItems.value;
    }
    if (itemToFocusId == null) {
      itemToFocusId = getFirstNavigableItem(instance);
    }
    innerFocusItem(event, itemToFocusId);
  });
  const removeFocusedItem = useEventCallback_default(() => {
    if (state.focusedItemId == null) {
      return;
    }
    const itemMeta = instance.getItemMeta(state.focusedItemId);
    if (itemMeta) {
      const itemElement = document.getElementById(instance.getTreeItemIdAttribute(state.focusedItemId, itemMeta.idAttribute));
      if (itemElement) {
        itemElement.blur();
      }
    }
    setFocusedItemId(null);
  });
  const canItemBeTabbed = (itemId) => itemId === tabbableItemId;
  useInstanceEventHandler(instance, "removeItem", ({
    id
  }) => {
    if (state.focusedItemId === id) {
      instance.focusDefaultItem(null);
    }
  });
  const createRootHandleFocus = (otherHandlers) => (event) => {
    var _a;
    (_a = otherHandlers.onFocus) == null ? void 0 : _a.call(otherHandlers, event);
    if (event.defaultMuiPrevented) {
      return;
    }
    if (event.target === event.currentTarget) {
      instance.focusDefaultItem(event);
    }
  };
  const focusedItem = instance.getItemMeta(state.focusedItemId);
  const activeDescendant = focusedItem ? instance.getTreeItemIdAttribute(focusedItem.id, focusedItem.idAttribute) : null;
  return {
    getRootProps: (otherHandlers) => ({
      onFocus: createRootHandleFocus(otherHandlers),
      "aria-activedescendant": activeDescendant ?? void 0
    }),
    publicAPI: {
      focusItem
    },
    instance: {
      isItemFocused,
      canItemBeTabbed,
      focusItem,
      focusDefaultItem,
      removeFocusedItem
    }
  };
};
useTreeViewFocus.getInitialState = () => ({
  focusedItemId: null
});
useTreeViewFocus.params = {
  onItemFocus: true
};

// node_modules/@mui/x-tree-view/internals/plugins/useTreeViewKeyboardNavigation/useTreeViewKeyboardNavigation.js
var React11 = __toESM(require_react());
function isPrintableCharacter(string) {
  return !!string && string.length === 1 && !!string.match(/\S/);
}
function findNextFirstChar(firstChars, startIndex, char) {
  for (let i = startIndex; i < firstChars.length; i += 1) {
    if (char === firstChars[i]) {
      return i;
    }
  }
  return -1;
}
var useTreeViewKeyboardNavigation = ({
  instance,
  params,
  state
}) => {
  const theme = useTheme();
  const isRTL = theme.direction === "rtl";
  const firstCharMap = React11.useRef({});
  const updateFirstCharMap = useEventCallback_default((callback) => {
    firstCharMap.current = callback(firstCharMap.current);
  });
  React11.useEffect(() => {
    if (instance.areItemUpdatesPrevented()) {
      return;
    }
    const newFirstCharMap = {};
    const processItem = (item) => {
      newFirstCharMap[item.id] = item.label.substring(0, 1).toLowerCase();
    };
    Object.values(state.items.itemMetaMap).forEach(processItem);
    firstCharMap.current = newFirstCharMap;
  }, [state.items.itemMetaMap, params.getItemId, instance]);
  const getFirstMatchingItem = (itemId, firstChar) => {
    let start;
    let index;
    const lowercaseChar = firstChar.toLowerCase();
    const firstCharIds = [];
    const firstChars = [];
    Object.keys(firstCharMap.current).forEach((mapItemId) => {
      const map = instance.getItemMeta(mapItemId);
      const visible = map.parentId ? instance.isItemExpanded(map.parentId) : true;
      const shouldBeSkipped = params.disabledItemsFocusable ? false : instance.isItemDisabled(mapItemId);
      if (visible && !shouldBeSkipped) {
        firstCharIds.push(mapItemId);
        firstChars.push(firstCharMap.current[mapItemId]);
      }
    });
    start = firstCharIds.indexOf(itemId) + 1;
    if (start >= firstCharIds.length) {
      start = 0;
    }
    index = findNextFirstChar(firstChars, start, lowercaseChar);
    if (index === -1) {
      index = findNextFirstChar(firstChars, 0, lowercaseChar);
    }
    if (index > -1) {
      return firstCharIds[index];
    }
    return null;
  };
  const canToggleItemSelection = (itemId) => !params.disableSelection && !instance.isItemDisabled(itemId);
  const canToggleItemExpansion = (itemId) => {
    return !instance.isItemDisabled(itemId) && instance.isItemExpandable(itemId);
  };
  const handleItemKeyDown = (event, itemId) => {
    if (event.defaultMuiPrevented) {
      return;
    }
    if (event.altKey || event.currentTarget !== event.target) {
      return;
    }
    const ctrlPressed = event.ctrlKey || event.metaKey;
    const key = event.key;
    switch (true) {
      case (key === " " && canToggleItemSelection(itemId)): {
        event.preventDefault();
        if (params.multiSelect && event.shiftKey) {
          instance.selectRange(event, {
            end: itemId
          });
        } else if (params.multiSelect) {
          instance.selectItem(event, itemId, true);
        } else {
          instance.selectItem(event, itemId);
        }
        break;
      }
      case key === "Enter": {
        if (canToggleItemExpansion(itemId)) {
          instance.toggleItemExpansion(event, itemId);
          event.preventDefault();
        } else if (canToggleItemSelection(itemId)) {
          if (params.multiSelect) {
            event.preventDefault();
            instance.selectItem(event, itemId, true);
          } else if (!instance.isItemSelected(itemId)) {
            instance.selectItem(event, itemId);
            event.preventDefault();
          }
        }
        break;
      }
      case key === "ArrowDown": {
        const nextItem = getNextNavigableItem(instance, itemId);
        if (nextItem) {
          event.preventDefault();
          instance.focusItem(event, nextItem);
          if (params.multiSelect && event.shiftKey && canToggleItemSelection(nextItem)) {
            instance.selectRange(event, {
              end: nextItem,
              current: itemId
            }, true);
          }
        }
        break;
      }
      case key === "ArrowUp": {
        const previousItem = getPreviousNavigableItem(instance, itemId);
        if (previousItem) {
          event.preventDefault();
          instance.focusItem(event, previousItem);
          if (params.multiSelect && event.shiftKey && canToggleItemSelection(previousItem)) {
            instance.selectRange(event, {
              end: previousItem,
              current: itemId
            }, true);
          }
        }
        break;
      }
      case (key === "ArrowRight" && !isRTL || key === "ArrowLeft" && isRTL): {
        if (instance.isItemExpanded(itemId)) {
          const nextItemId = getNextNavigableItem(instance, itemId);
          if (nextItemId) {
            instance.focusItem(event, nextItemId);
            event.preventDefault();
          }
        } else if (canToggleItemExpansion(itemId)) {
          instance.toggleItemExpansion(event, itemId);
          event.preventDefault();
        }
        break;
      }
      case (key === "ArrowLeft" && !isRTL || key === "ArrowRight" && isRTL): {
        if (canToggleItemExpansion(itemId) && instance.isItemExpanded(itemId)) {
          instance.toggleItemExpansion(event, itemId);
          event.preventDefault();
        } else {
          const parent = instance.getItemMeta(itemId).parentId;
          if (parent) {
            instance.focusItem(event, parent);
            event.preventDefault();
          }
        }
        break;
      }
      case key === "Home": {
        instance.focusItem(event, getFirstNavigableItem(instance));
        if (canToggleItemSelection(itemId) && params.multiSelect && ctrlPressed && event.shiftKey) {
          instance.rangeSelectToFirst(event, itemId);
        }
        event.preventDefault();
        break;
      }
      case key === "End": {
        instance.focusItem(event, getLastNavigableItem(instance));
        if (canToggleItemSelection(itemId) && params.multiSelect && ctrlPressed && event.shiftKey) {
          instance.rangeSelectToLast(event, itemId);
        }
        event.preventDefault();
        break;
      }
      case key === "*": {
        instance.expandAllSiblings(event, itemId);
        event.preventDefault();
        break;
      }
      case (key === "a" && ctrlPressed && params.multiSelect && !params.disableSelection): {
        instance.selectRange(event, {
          start: getFirstNavigableItem(instance),
          end: getLastNavigableItem(instance)
        });
        event.preventDefault();
        break;
      }
      case (!ctrlPressed && !event.shiftKey && isPrintableCharacter(key)): {
        const matchingItem = getFirstMatchingItem(itemId, key);
        if (matchingItem != null) {
          instance.focusItem(event, matchingItem);
          event.preventDefault();
        }
        break;
      }
    }
  };
  return {
    instance: {
      updateFirstCharMap,
      handleItemKeyDown
    }
  };
};
useTreeViewKeyboardNavigation.params = {};

// node_modules/@mui/x-tree-view/internals/plugins/useTreeViewIcons/useTreeViewIcons.js
var useTreeViewIcons = ({
  slots,
  slotProps
}) => {
  return {
    contextValue: {
      icons: {
        slots: {
          collapseIcon: slots.collapseIcon,
          expandIcon: slots.expandIcon,
          endIcon: slots.endIcon
        },
        slotProps: {
          collapseIcon: slotProps.collapseIcon,
          expandIcon: slotProps.expandIcon,
          endIcon: slotProps.endIcon
        }
      }
    }
  };
};
useTreeViewIcons.params = {};

// node_modules/@mui/x-tree-view/internals/plugins/defaultPlugins.js
var DEFAULT_TREE_VIEW_PLUGINS = [useTreeViewId, useTreeViewItems, useTreeViewExpansion, useTreeViewSelection, useTreeViewFocus, useTreeViewKeyboardNavigation, useTreeViewIcons];

// node_modules/@mui/x-tree-view/internals/plugins/useTreeViewJSXItems/useTreeViewJSXItems.js
init_extends();
var React13 = __toESM(require_react());

// node_modules/@mui/x-tree-view/internals/TreeViewProvider/TreeViewChildrenItemProvider.js
var React12 = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var TreeViewChildrenItemContext = React12.createContext(null);
if (true) {
  TreeViewChildrenItemContext.displayName = "TreeViewChildrenItemContext";
}
function TreeViewChildrenItemProvider(props) {
  const {
    children,
    itemId = null
  } = props;
  const {
    instance,
    rootRef
  } = useTreeViewContext();
  const childrenIdAttrToIdRef = React12.useRef(/* @__PURE__ */ new Map());
  React12.useEffect(() => {
    if (!rootRef.current) {
      return;
    }
    let idAttr = null;
    if (itemId == null) {
      idAttr = rootRef.current.id;
    } else {
      const itemMeta = instance.getItemMeta(itemId);
      if (itemMeta !== void 0) {
        idAttr = instance.getTreeItemIdAttribute(itemId, itemMeta.idAttribute);
      }
    }
    if (idAttr == null) {
      return;
    }
    const previousChildrenIds = instance.getItemOrderedChildrenIds(itemId ?? null) ?? [];
    const childrenElements = rootRef.current.querySelectorAll(`${itemId == null ? "" : `*[id="${idAttr}"] `}[role="treeitem"]:not(*[id="${idAttr}"] [role="treeitem"] [role="treeitem"])`);
    const childrenIds = Array.from(childrenElements).map((child) => childrenIdAttrToIdRef.current.get(child.id));
    const hasChanged = childrenIds.length !== previousChildrenIds.length || childrenIds.some((childId, index) => childId !== previousChildrenIds[index]);
    if (hasChanged) {
      instance.setJSXItemsOrderedChildrenIds(itemId ?? null, childrenIds);
    }
  });
  const value = React12.useMemo(() => ({
    registerChild: (childIdAttribute, childItemId) => childrenIdAttrToIdRef.current.set(childIdAttribute, childItemId),
    unregisterChild: (childIdAttribute) => childrenIdAttrToIdRef.current.delete(childIdAttribute),
    parentId: itemId
  }), [itemId]);
  return (0, import_jsx_runtime2.jsx)(TreeViewChildrenItemContext.Provider, {
    value,
    children
  });
}
true ? TreeViewChildrenItemProvider.propTypes = {
  children: import_prop_types.default.node,
  id: import_prop_types.default.string
} : void 0;

// node_modules/@mui/x-tree-view/internals/plugins/useTreeViewJSXItems/useTreeViewJSXItems.js
var import_jsx_runtime3 = __toESM(require_jsx_runtime());
var useTreeViewJSXItems = ({
  instance,
  setState
}) => {
  instance.preventItemUpdates();
  const insertJSXItem = useEventCallback_default((item) => {
    setState((prevState) => {
      if (prevState.items.itemMetaMap[item.id] != null) {
        throw new Error(["MUI X: The Tree View component requires all items to have a unique `id` property.", "Alternatively, you can use the `getItemId` prop to specify a custom id for each item.", `Two items were provided with the same id in the \`items\` prop: "${item.id}"`].join("\n"));
      }
      return _extends({}, prevState, {
        items: _extends({}, prevState.items, {
          itemMetaMap: _extends({}, prevState.items.itemMetaMap, {
            [item.id]: item
          }),
          // For `SimpleTreeView`, we don't have a proper `item` object, so we create a very basic one.
          itemMap: _extends({}, prevState.items.itemMap, {
            [item.id]: {
              id: item.id,
              label: item.label
            }
          })
        })
      });
    });
  });
  const setJSXItemsOrderedChildrenIds = (parentId, orderedChildrenIds) => {
    const parentIdWithDefault = parentId ?? TREE_VIEW_ROOT_PARENT_ID;
    setState((prevState) => _extends({}, prevState, {
      items: _extends({}, prevState.items, {
        itemOrderedChildrenIds: _extends({}, prevState.items.itemOrderedChildrenIds, {
          [parentIdWithDefault]: orderedChildrenIds
        }),
        itemChildrenIndexes: _extends({}, prevState.items.itemChildrenIndexes, {
          [parentIdWithDefault]: buildSiblingIndexes(orderedChildrenIds)
        })
      })
    }));
  };
  const removeJSXItem = useEventCallback_default((itemId) => {
    setState((prevState) => {
      const newItemMetaMap = _extends({}, prevState.items.itemMetaMap);
      const newItemMap = _extends({}, prevState.items.itemMap);
      delete newItemMetaMap[itemId];
      delete newItemMap[itemId];
      return _extends({}, prevState, {
        items: _extends({}, prevState.items, {
          itemMetaMap: newItemMetaMap,
          itemMap: newItemMap
        })
      });
    });
    publishTreeViewEvent(instance, "removeItem", {
      id: itemId
    });
  });
  const mapFirstCharFromJSX = useEventCallback_default((itemId, firstChar) => {
    instance.updateFirstCharMap((firstCharMap) => {
      firstCharMap[itemId] = firstChar;
      return firstCharMap;
    });
    return () => {
      instance.updateFirstCharMap((firstCharMap) => {
        const newMap = _extends({}, firstCharMap);
        delete newMap[itemId];
        return newMap;
      });
    };
  });
  return {
    instance: {
      insertJSXItem,
      removeJSXItem,
      setJSXItemsOrderedChildrenIds,
      mapFirstCharFromJSX
    }
  };
};
var isItemExpandable = (reactChildren) => {
  if (Array.isArray(reactChildren)) {
    return reactChildren.length > 0 && reactChildren.some(isItemExpandable);
  }
  return Boolean(reactChildren);
};
var useTreeViewJSXItemsItemPlugin = ({
  props,
  rootRef,
  contentRef
}) => {
  const {
    instance
  } = useTreeViewContext();
  const {
    children,
    disabled = false,
    label,
    itemId,
    id
  } = props;
  const parentContext = React13.useContext(TreeViewChildrenItemContext);
  if (parentContext == null) {
    throw new Error(["MUI X: Could not find the Tree View Children Item context.", "It looks like you rendered your component outside of a SimpleTreeView parent component.", "This can also happen if you are bundling multiple versions of the Tree View."].join("\n"));
  }
  const {
    registerChild,
    unregisterChild,
    parentId
  } = parentContext;
  const expandable = isItemExpandable(children);
  const pluginContentRef = React13.useRef(null);
  const handleContentRef = useForkRef(pluginContentRef, contentRef);
  useEnhancedEffect_default(() => {
    const idAttributeWithDefault = instance.getTreeItemIdAttribute(itemId, id);
    registerChild(idAttributeWithDefault, itemId);
    return () => {
      unregisterChild(idAttributeWithDefault);
    };
  }, [instance, registerChild, unregisterChild, itemId, id]);
  React13.useEffect(() => {
    instance.insertJSXItem({
      id: itemId,
      idAttribute: id,
      parentId,
      expandable,
      disabled
    });
    return () => instance.removeJSXItem(itemId);
  }, [instance, parentId, itemId, expandable, disabled, id]);
  React13.useEffect(() => {
    var _a;
    if (label) {
      return instance.mapFirstCharFromJSX(itemId, (((_a = pluginContentRef.current) == null ? void 0 : _a.textContent) ?? "").substring(0, 1).toLowerCase());
    }
    return void 0;
  }, [instance, itemId, label]);
  return {
    contentRef: handleContentRef,
    rootRef
  };
};
useTreeViewJSXItems.itemPlugin = useTreeViewJSXItemsItemPlugin;
useTreeViewJSXItems.wrapItem = ({
  children,
  itemId
}) => (0, import_jsx_runtime3.jsx)(TreeViewChildrenItemProvider, {
  itemId,
  children
});
useTreeViewJSXItems.wrapRoot = ({
  children
}) => (0, import_jsx_runtime3.jsx)(TreeViewChildrenItemProvider, {
  children
});
useTreeViewJSXItems.params = {};

// node_modules/@mui/x-tree-view/SimpleTreeView/SimpleTreeView.plugins.js
var SIMPLE_TREE_VIEW_PLUGINS = [...DEFAULT_TREE_VIEW_PLUGINS, useTreeViewJSXItems];

// node_modules/@mui/x-tree-view/internals/utils/warning.js
var buildWarning = (message, gravity = "warning") => {
  let alreadyWarned = false;
  const cleanMessage = Array.isArray(message) ? message.join("\n") : message;
  return () => {
    if (!alreadyWarned) {
      alreadyWarned = true;
      if (gravity === "error") {
        console.error(cleanMessage);
      } else {
        console.warn(cleanMessage);
      }
    }
  };
};

// node_modules/@mui/x-tree-view/internals/utils/extractPluginParamsFromProps.js
var _excluded = ["slots", "slotProps", "apiRef"];
var extractPluginParamsFromProps = (_ref) => {
  let {
    props: {
      slots,
      slotProps,
      apiRef
    },
    plugins,
    rootRef
  } = _ref, props = _objectWithoutPropertiesLoose(_ref.props, _excluded);
  const paramsLookup = {};
  plugins.forEach((plugin) => {
    Object.assign(paramsLookup, plugin.params);
  });
  const pluginParams = {
    plugins,
    rootRef,
    slots: slots ?? {},
    slotProps: slotProps ?? {},
    apiRef
  };
  const otherProps = {};
  Object.keys(props).forEach((propName) => {
    const prop = props[propName];
    if (paramsLookup[propName]) {
      pluginParams[propName] = prop;
    } else {
      otherProps[propName] = prop;
    }
  });
  return {
    pluginParams,
    slots,
    slotProps,
    otherProps
  };
};

// node_modules/@mui/x-tree-view/SimpleTreeView/SimpleTreeView.js
var import_jsx_runtime4 = __toESM(require_jsx_runtime());
var useUtilityClasses = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"]
  };
  return composeClasses(slots, getSimpleTreeViewUtilityClass, classes);
};
var SimpleTreeViewRoot = styled_default("ul", {
  name: "MuiSimpleTreeView",
  slot: "Root",
  overridesResolver: (props, styles) => styles.root
})({
  padding: 0,
  margin: 0,
  listStyle: "none",
  outline: 0,
  position: "relative"
});
var EMPTY_ITEMS = [];
var itemsPropWarning = buildWarning(["MUI X: The `SimpleTreeView` component does not support the `items` prop.", "If you want to add items, you need to pass them as JSX children.", "Check the documentation for more details: https://mui.com/x/react-tree-view/simple-tree-view/items/"]);
var SimpleTreeView = React14.forwardRef(function SimpleTreeView2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiSimpleTreeView"
  });
  const ownerState = props;
  if (true) {
    if (props.items != null) {
      itemsPropWarning();
    }
  }
  const {
    pluginParams,
    slots,
    slotProps,
    otherProps
  } = extractPluginParamsFromProps({
    props: _extends({}, props, {
      items: EMPTY_ITEMS
    }),
    plugins: SIMPLE_TREE_VIEW_PLUGINS,
    rootRef: ref
  });
  const {
    getRootProps,
    contextValue
  } = useTreeView(pluginParams);
  const classes = useUtilityClasses(props);
  const Root = (slots == null ? void 0 : slots.root) ?? SimpleTreeViewRoot;
  const rootProps = useSlotProps({
    elementType: Root,
    externalSlotProps: slotProps == null ? void 0 : slotProps.root,
    externalForwardedProps: otherProps,
    className: classes.root,
    getSlotProps: getRootProps,
    ownerState
  });
  return (0, import_jsx_runtime4.jsx)(TreeViewProvider, {
    value: contextValue,
    children: (0, import_jsx_runtime4.jsx)(Root, _extends({}, rootProps))
  });
});
true ? SimpleTreeView.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The ref object that allows Tree View manipulation. Can be instantiated with `useTreeViewApiRef()`.
   */
  apiRef: import_prop_types2.default.shape({
    current: import_prop_types2.default.shape({
      focusItem: import_prop_types2.default.func.isRequired,
      getItem: import_prop_types2.default.func.isRequired,
      setItemExpansion: import_prop_types2.default.func.isRequired
    })
  }),
  /**
   * The content of the component.
   */
  children: import_prop_types2.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types2.default.object,
  className: import_prop_types2.default.string,
  /**
   * Expanded item ids.
   * Used when the item's expansion is not controlled.
   * @default []
   */
  defaultExpandedItems: import_prop_types2.default.arrayOf(import_prop_types2.default.string),
  /**
   * Selected item ids. (Uncontrolled)
   * When `multiSelect` is true this takes an array of strings; when false (default) a string.
   * @default []
   */
  defaultSelectedItems: import_prop_types2.default.any,
  /**
   * If `true`, will allow focus on disabled items.
   * @default false
   */
  disabledItemsFocusable: import_prop_types2.default.bool,
  /**
   * If `true` selection is disabled.
   * @default false
   */
  disableSelection: import_prop_types2.default.bool,
  /**
   * Expanded item ids.
   * Used when the item's expansion is controlled.
   */
  expandedItems: import_prop_types2.default.arrayOf(import_prop_types2.default.string),
  /**
   * This prop is used to help implement the accessibility logic.
   * If you don't provide this prop. It falls back to a randomly generated id.
   */
  id: import_prop_types2.default.string,
  /**
   * If true `ctrl` and `shift` will trigger multiselect.
   * @default false
   */
  multiSelect: import_prop_types2.default.bool,
  /**
   * Callback fired when tree items are expanded/collapsed.
   * @param {React.SyntheticEvent} event The event source of the callback.
   * @param {array} itemIds The ids of the expanded items.
   */
  onExpandedItemsChange: import_prop_types2.default.func,
  /**
   * Callback fired when a tree item is expanded or collapsed.
   * @param {React.SyntheticEvent} event The event source of the callback.
   * @param {array} itemId The itemId of the modified item.
   * @param {array} isExpanded `true` if the item has just been expanded, `false` if it has just been collapsed.
   */
  onItemExpansionToggle: import_prop_types2.default.func,
  /**
   * Callback fired when tree items are focused.
   * @param {React.SyntheticEvent} event The event source of the callback **Warning**: This is a generic event not a focus event.
   * @param {string} itemId The id of the focused item.
   * @param {string} value of the focused item.
   */
  onItemFocus: import_prop_types2.default.func,
  /**
   * Callback fired when a tree item is selected or deselected.
   * @param {React.SyntheticEvent} event The event source of the callback.
   * @param {array} itemId The itemId of the modified item.
   * @param {array} isSelected `true` if the item has just been selected, `false` if it has just been deselected.
   */
  onItemSelectionToggle: import_prop_types2.default.func,
  /**
   * Callback fired when tree items are selected/deselected.
   * @param {React.SyntheticEvent} event The event source of the callback
   * @param {string[] | string} itemIds The ids of the selected items.
   * When `multiSelect` is `true`, this is an array of strings; when false (default) a string.
   */
  onSelectedItemsChange: import_prop_types2.default.func,
  /**
   * Selected item ids. (Controlled)
   * When `multiSelect` is true this takes an array of strings; when false (default) a string.
   */
  selectedItems: import_prop_types2.default.any,
  /**
   * The props used for each component slot.
   */
  slotProps: import_prop_types2.default.object,
  /**
   * Overridable component slots.
   */
  slots: import_prop_types2.default.object,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types2.default.oneOfType([import_prop_types2.default.arrayOf(import_prop_types2.default.oneOfType([import_prop_types2.default.func, import_prop_types2.default.object, import_prop_types2.default.bool])), import_prop_types2.default.func, import_prop_types2.default.object])
} : void 0;

// node_modules/@mui/x-tree-view/TreeView/TreeView.js
var import_jsx_runtime5 = __toESM(require_jsx_runtime());
var useUtilityClasses2 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"]
  };
  return composeClasses(slots, getTreeViewUtilityClass, classes);
};
var TreeViewRoot = styled_default(SimpleTreeViewRoot, {
  name: "MuiTreeView",
  slot: "Root",
  overridesResolver: (props, styles) => styles.root
})({});
var warnedOnce = false;
var warn = () => {
  if (!warnedOnce) {
    console.warn(["MUI X: The TreeView component was renamed SimpleTreeView.", "The component with the old naming will be removed in the version v8.0.0.", "", "You should use `import { SimpleTreeView } from '@mui/x-tree-view'`", "or `import { SimpleTreeView } from '@mui/x-tree-view/TreeView'`"].join("\n"));
    warnedOnce = true;
  }
};
var TreeView = React15.forwardRef(function TreeView2(inProps, ref) {
  if (true) {
    warn();
  }
  const props = useThemeProps({
    props: inProps,
    name: "MuiTreeView"
  });
  const classes = useUtilityClasses2(props);
  return (0, import_jsx_runtime5.jsx)(SimpleTreeView, _extends({}, props, {
    ref,
    classes,
    slots: _extends({
      root: TreeViewRoot
    }, props.slots)
  }));
});
true ? TreeView.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The ref object that allows Tree View manipulation. Can be instantiated with `useTreeViewApiRef()`.
   */
  apiRef: import_prop_types3.default.shape({
    current: import_prop_types3.default.shape({
      focusItem: import_prop_types3.default.func.isRequired,
      getItem: import_prop_types3.default.func.isRequired,
      setItemExpansion: import_prop_types3.default.func.isRequired
    })
  }),
  /**
   * The content of the component.
   */
  children: import_prop_types3.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types3.default.object,
  className: import_prop_types3.default.string,
  /**
   * Expanded item ids.
   * Used when the item's expansion is not controlled.
   * @default []
   */
  defaultExpandedItems: import_prop_types3.default.arrayOf(import_prop_types3.default.string),
  /**
   * Selected item ids. (Uncontrolled)
   * When `multiSelect` is true this takes an array of strings; when false (default) a string.
   * @default []
   */
  defaultSelectedItems: import_prop_types3.default.any,
  /**
   * If `true`, will allow focus on disabled items.
   * @default false
   */
  disabledItemsFocusable: import_prop_types3.default.bool,
  /**
   * If `true` selection is disabled.
   * @default false
   */
  disableSelection: import_prop_types3.default.bool,
  /**
   * Expanded item ids.
   * Used when the item's expansion is controlled.
   */
  expandedItems: import_prop_types3.default.arrayOf(import_prop_types3.default.string),
  /**
   * This prop is used to help implement the accessibility logic.
   * If you don't provide this prop. It falls back to a randomly generated id.
   */
  id: import_prop_types3.default.string,
  /**
   * If true `ctrl` and `shift` will trigger multiselect.
   * @default false
   */
  multiSelect: import_prop_types3.default.bool,
  /**
   * Callback fired when tree items are expanded/collapsed.
   * @param {React.SyntheticEvent} event The event source of the callback.
   * @param {array} itemIds The ids of the expanded items.
   */
  onExpandedItemsChange: import_prop_types3.default.func,
  /**
   * Callback fired when a tree item is expanded or collapsed.
   * @param {React.SyntheticEvent} event The event source of the callback.
   * @param {array} itemId The itemId of the modified item.
   * @param {array} isExpanded `true` if the item has just been expanded, `false` if it has just been collapsed.
   */
  onItemExpansionToggle: import_prop_types3.default.func,
  /**
   * Callback fired when tree items are focused.
   * @param {React.SyntheticEvent} event The event source of the callback **Warning**: This is a generic event not a focus event.
   * @param {string} itemId The id of the focused item.
   * @param {string} value of the focused item.
   */
  onItemFocus: import_prop_types3.default.func,
  /**
   * Callback fired when a tree item is selected or deselected.
   * @param {React.SyntheticEvent} event The event source of the callback.
   * @param {array} itemId The itemId of the modified item.
   * @param {array} isSelected `true` if the item has just been selected, `false` if it has just been deselected.
   */
  onItemSelectionToggle: import_prop_types3.default.func,
  /**
   * Callback fired when tree items are selected/deselected.
   * @param {React.SyntheticEvent} event The event source of the callback
   * @param {string[] | string} itemIds The ids of the selected items.
   * When `multiSelect` is `true`, this is an array of strings; when false (default) a string.
   */
  onSelectedItemsChange: import_prop_types3.default.func,
  /**
   * Selected item ids. (Controlled)
   * When `multiSelect` is true this takes an array of strings; when false (default) a string.
   */
  selectedItems: import_prop_types3.default.any,
  /**
   * The props used for each component slot.
   */
  slotProps: import_prop_types3.default.object,
  /**
   * Overridable component slots.
   */
  slots: import_prop_types3.default.object,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types3.default.oneOfType([import_prop_types3.default.arrayOf(import_prop_types3.default.oneOfType([import_prop_types3.default.func, import_prop_types3.default.object, import_prop_types3.default.bool])), import_prop_types3.default.func, import_prop_types3.default.object])
} : void 0;

// node_modules/@mui/x-tree-view/RichTreeView/RichTreeView.js
init_extends();
var React16 = __toESM(require_react());
var import_prop_types4 = __toESM(require_prop_types());

// node_modules/@mui/x-tree-view/RichTreeView/richTreeViewClasses.js
function getRichTreeViewUtilityClass(slot) {
  return generateUtilityClass("MuiRichTreeView", slot);
}
var richTreeViewClasses = generateUtilityClasses("MuiRichTreeView", ["root"]);

// node_modules/@mui/x-tree-view/RichTreeView/RichTreeView.js
var import_jsx_runtime6 = __toESM(require_jsx_runtime());
var useUtilityClasses3 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"]
  };
  return composeClasses(slots, getRichTreeViewUtilityClass, classes);
};
var RichTreeViewRoot = styled_default("ul", {
  name: "MuiRichTreeView",
  slot: "Root",
  overridesResolver: (props, styles) => styles.root
})({
  padding: 0,
  margin: 0,
  listStyle: "none",
  outline: 0,
  position: "relative"
});
function WrappedTreeItem({
  slots,
  slotProps,
  label,
  id,
  itemId,
  children
}) {
  const Item = (slots == null ? void 0 : slots.item) ?? TreeItem;
  const itemProps = useSlotProps({
    elementType: Item,
    externalSlotProps: slotProps == null ? void 0 : slotProps.item,
    additionalProps: {
      itemId,
      id,
      label
    },
    ownerState: {
      itemId,
      label
    }
  });
  return (0, import_jsx_runtime6.jsx)(Item, _extends({}, itemProps, {
    children
  }));
}
var childrenWarning = buildWarning(["MUI X: The `RichTreeView` component does not support JSX children.", "If you want to add items, you need to use the `items` prop", "Check the documentation for more details: https://mui.com/x/react-tree-view/rich-tree-view/items/"]);
var RichTreeView = React16.forwardRef(function RichTreeView2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiRichTreeView"
  });
  if (true) {
    if (props.children != null) {
      childrenWarning();
    }
  }
  const {
    pluginParams,
    slots,
    slotProps,
    otherProps
  } = extractPluginParamsFromProps({
    props,
    plugins: DEFAULT_TREE_VIEW_PLUGINS,
    rootRef: ref
  });
  const {
    getRootProps,
    contextValue,
    instance
  } = useTreeView(pluginParams);
  const classes = useUtilityClasses3(props);
  const Root = (slots == null ? void 0 : slots.root) ?? RichTreeViewRoot;
  const rootProps = useSlotProps({
    elementType: Root,
    externalSlotProps: slotProps == null ? void 0 : slotProps.root,
    externalForwardedProps: otherProps,
    className: classes.root,
    getSlotProps: getRootProps,
    ownerState: props
  });
  const itemsToRender = instance.getItemsToRender();
  const renderItem = ({
    label,
    itemId,
    id,
    children
  }) => {
    return (0, import_jsx_runtime6.jsx)(WrappedTreeItem, {
      slots,
      slotProps,
      label,
      id,
      itemId,
      children: children == null ? void 0 : children.map(renderItem)
    }, itemId);
  };
  return (0, import_jsx_runtime6.jsx)(TreeViewProvider, {
    value: contextValue,
    children: (0, import_jsx_runtime6.jsx)(Root, _extends({}, rootProps, {
      children: itemsToRender.map(renderItem)
    }))
  });
});
true ? RichTreeView.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The ref object that allows Tree View manipulation. Can be instantiated with `useTreeViewApiRef()`.
   */
  apiRef: import_prop_types4.default.shape({
    current: import_prop_types4.default.shape({
      focusItem: import_prop_types4.default.func.isRequired,
      getItem: import_prop_types4.default.func.isRequired,
      setItemExpansion: import_prop_types4.default.func.isRequired
    })
  }),
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types4.default.object,
  className: import_prop_types4.default.string,
  /**
   * Expanded item ids.
   * Used when the item's expansion is not controlled.
   * @default []
   */
  defaultExpandedItems: import_prop_types4.default.arrayOf(import_prop_types4.default.string),
  /**
   * Selected item ids. (Uncontrolled)
   * When `multiSelect` is true this takes an array of strings; when false (default) a string.
   * @default []
   */
  defaultSelectedItems: import_prop_types4.default.any,
  /**
   * If `true`, will allow focus on disabled items.
   * @default false
   */
  disabledItemsFocusable: import_prop_types4.default.bool,
  /**
   * If `true` selection is disabled.
   * @default false
   */
  disableSelection: import_prop_types4.default.bool,
  /**
   * Expanded item ids.
   * Used when the item's expansion is controlled.
   */
  expandedItems: import_prop_types4.default.arrayOf(import_prop_types4.default.string),
  /**
   * Used to determine the id of a given item.
   *
   * @template R
   * @param {R} item The item to check.
   * @returns {string} The id of the item.
   * @default (item) => item.id
   */
  getItemId: import_prop_types4.default.func,
  /**
   * Used to determine the string label for a given item.
   *
   * @template R
   * @param {R} item The item to check.
   * @returns {string} The label of the item.
   * @default (item) => item.label
   */
  getItemLabel: import_prop_types4.default.func,
  /**
   * This prop is used to help implement the accessibility logic.
   * If you don't provide this prop. It falls back to a randomly generated id.
   */
  id: import_prop_types4.default.string,
  /**
   * Used to determine if a given item should be disabled.
   * @template R
   * @param {R} item The item to check.
   * @returns {boolean} `true` if the item should be disabled.
   */
  isItemDisabled: import_prop_types4.default.func,
  items: import_prop_types4.default.array.isRequired,
  /**
   * If true `ctrl` and `shift` will trigger multiselect.
   * @default false
   */
  multiSelect: import_prop_types4.default.bool,
  /**
   * Callback fired when tree items are expanded/collapsed.
   * @param {React.SyntheticEvent} event The event source of the callback.
   * @param {array} itemIds The ids of the expanded items.
   */
  onExpandedItemsChange: import_prop_types4.default.func,
  /**
   * Callback fired when a tree item is expanded or collapsed.
   * @param {React.SyntheticEvent} event The event source of the callback.
   * @param {array} itemId The itemId of the modified item.
   * @param {array} isExpanded `true` if the item has just been expanded, `false` if it has just been collapsed.
   */
  onItemExpansionToggle: import_prop_types4.default.func,
  /**
   * Callback fired when tree items are focused.
   * @param {React.SyntheticEvent} event The event source of the callback **Warning**: This is a generic event not a focus event.
   * @param {string} itemId The id of the focused item.
   * @param {string} value of the focused item.
   */
  onItemFocus: import_prop_types4.default.func,
  /**
   * Callback fired when a tree item is selected or deselected.
   * @param {React.SyntheticEvent} event The event source of the callback.
   * @param {array} itemId The itemId of the modified item.
   * @param {array} isSelected `true` if the item has just been selected, `false` if it has just been deselected.
   */
  onItemSelectionToggle: import_prop_types4.default.func,
  /**
   * Callback fired when tree items are selected/deselected.
   * @param {React.SyntheticEvent} event The event source of the callback
   * @param {string[] | string} itemIds The ids of the selected items.
   * When `multiSelect` is `true`, this is an array of strings; when false (default) a string.
   */
  onSelectedItemsChange: import_prop_types4.default.func,
  /**
   * Selected item ids. (Controlled)
   * When `multiSelect` is true this takes an array of strings; when false (default) a string.
   */
  selectedItems: import_prop_types4.default.any,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types4.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types4.default.object,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types4.default.oneOfType([import_prop_types4.default.arrayOf(import_prop_types4.default.oneOfType([import_prop_types4.default.func, import_prop_types4.default.object, import_prop_types4.default.bool])), import_prop_types4.default.func, import_prop_types4.default.object])
} : void 0;

// node_modules/@mui/x-tree-view/TreeItem2/TreeItem2.js
init_extends();
var React18 = __toESM(require_react());
var import_prop_types6 = __toESM(require_prop_types());

// node_modules/@mui/x-tree-view/useTreeItem2/useTreeItem2.js
init_extends();

// node_modules/@mui/x-tree-view/hooks/useTreeItem2Utils/useTreeItem2Utils.js
var useTreeItem2Utils = ({
  itemId,
  children
}) => {
  const {
    instance,
    selection: {
      multiSelect
    }
  } = useTreeViewContext();
  const status = {
    expandable: Boolean(Array.isArray(children) ? children.length : children),
    expanded: instance.isItemExpanded(itemId),
    focused: instance.isItemFocused(itemId),
    selected: instance.isItemSelected(itemId),
    disabled: instance.isItemDisabled(itemId)
  };
  const handleExpansion = (event) => {
    if (status.disabled) {
      return;
    }
    if (!status.focused) {
      instance.focusItem(event, itemId);
    }
    const multiple = multiSelect && (event.shiftKey || event.ctrlKey || event.metaKey);
    if (status.expandable && !(multiple && instance.isItemExpanded(itemId))) {
      instance.toggleItemExpansion(event, itemId);
    }
  };
  const handleSelection = (event) => {
    if (status.disabled) {
      return;
    }
    if (!status.focused) {
      instance.focusItem(event, itemId);
    }
    const multiple = multiSelect && (event.shiftKey || event.ctrlKey || event.metaKey);
    if (multiple) {
      if (event.shiftKey) {
        instance.selectRange(event, {
          end: itemId
        });
      } else {
        instance.selectItem(event, itemId, true);
      }
    } else {
      instance.selectItem(event, itemId);
    }
  };
  const interactions = {
    handleExpansion,
    handleSelection
  };
  return {
    interactions,
    status
  };
};

// node_modules/@mui/x-tree-view/useTreeItem2/useTreeItem2.js
var useTreeItem2 = (parameters) => {
  const {
    runItemPlugins,
    selection: {
      multiSelect
    },
    disabledItemsFocusable,
    instance,
    publicAPI
  } = useTreeViewContext();
  const {
    id,
    itemId,
    label,
    children,
    rootRef
  } = parameters;
  const {
    rootRef: pluginRootRef,
    contentRef
  } = runItemPlugins(parameters);
  const {
    interactions,
    status
  } = useTreeItem2Utils({
    itemId,
    children
  });
  const idAttribute = instance.getTreeItemIdAttribute(itemId, id);
  const handleRootRef = useForkRef(rootRef, pluginRootRef);
  const createRootHandleFocus = (otherHandlers) => (event) => {
    var _a;
    (_a = otherHandlers.onFocus) == null ? void 0 : _a.call(otherHandlers, event);
    if (event.defaultMuiPrevented) {
      return;
    }
    const canBeFocused = !status.disabled || disabledItemsFocusable;
    if (!status.focused && canBeFocused && event.currentTarget === event.target) {
      instance.focusItem(event, itemId);
    }
  };
  const createRootHandleBlur = (otherHandlers) => (event) => {
    var _a;
    (_a = otherHandlers.onBlur) == null ? void 0 : _a.call(otherHandlers, event);
    if (event.defaultMuiPrevented) {
      return;
    }
    instance.removeFocusedItem();
  };
  const createRootHandleKeyDown = (otherHandlers) => (event) => {
    var _a;
    (_a = otherHandlers.onKeyDown) == null ? void 0 : _a.call(otherHandlers, event);
    if (event.defaultMuiPrevented) {
      return;
    }
    instance.handleItemKeyDown(event, itemId);
  };
  const createContentHandleClick = (otherHandlers) => (event) => {
    var _a;
    (_a = otherHandlers.onClick) == null ? void 0 : _a.call(otherHandlers, event);
    if (event.defaultMuiPrevented) {
      return;
    }
    interactions.handleExpansion(event);
    interactions.handleSelection(event);
  };
  const createContentHandleMouseDown = (otherHandlers) => (event) => {
    var _a;
    (_a = otherHandlers.onMouseDown) == null ? void 0 : _a.call(otherHandlers, event);
    if (event.defaultMuiPrevented) {
      return;
    }
    if (event.shiftKey || event.ctrlKey || event.metaKey || status.disabled) {
      event.preventDefault();
    }
  };
  const getRootProps = (externalProps = {}) => {
    const externalEventHandlers = _extends({}, extractEventHandlers(parameters), extractEventHandlers(externalProps));
    let ariaSelected;
    if (multiSelect) {
      ariaSelected = status.selected;
    } else if (status.selected) {
      ariaSelected = true;
    }
    return _extends({}, externalEventHandlers, {
      ref: handleRootRef,
      role: "treeitem",
      tabIndex: instance.canItemBeTabbed(itemId) ? 0 : -1,
      id: idAttribute,
      "aria-expanded": status.expandable ? status.expanded : void 0,
      "aria-selected": ariaSelected,
      "aria-disabled": status.disabled || void 0
    }, externalProps, {
      onFocus: createRootHandleFocus(externalEventHandlers),
      onBlur: createRootHandleBlur(externalEventHandlers),
      onKeyDown: createRootHandleKeyDown(externalEventHandlers)
    });
  };
  const getContentProps = (externalProps = {}) => {
    const externalEventHandlers = _extends({}, extractEventHandlers(parameters), extractEventHandlers(externalProps));
    return _extends({}, externalEventHandlers, externalProps, {
      ref: contentRef,
      onClick: createContentHandleClick(externalEventHandlers),
      onMouseDown: createContentHandleMouseDown(externalEventHandlers),
      status
    });
  };
  const getLabelProps = (externalProps = {}) => {
    const externalEventHandlers = _extends({}, extractEventHandlers(parameters), extractEventHandlers(externalProps));
    return _extends({}, externalEventHandlers, {
      children: label
    }, externalProps);
  };
  const getIconContainerProps = (externalProps = {}) => {
    const externalEventHandlers = _extends({}, extractEventHandlers(parameters), extractEventHandlers(externalProps));
    return _extends({}, externalEventHandlers, externalProps);
  };
  const getGroupTransitionProps = (externalProps = {}) => {
    const externalEventHandlers = _extends({}, extractEventHandlers(parameters), extractEventHandlers(externalProps));
    return _extends({}, externalEventHandlers, {
      unmountOnExit: true,
      component: "ul",
      role: "group",
      in: status.expanded,
      children
    }, externalProps);
  };
  return {
    getRootProps,
    getContentProps,
    getGroupTransitionProps,
    getIconContainerProps,
    getLabelProps,
    rootRef: handleRootRef,
    status,
    publicAPI
  };
};

// node_modules/@mui/x-tree-view/TreeItem2Icon/TreeItem2Icon.js
init_extends();
var React17 = __toESM(require_react());
var import_prop_types5 = __toESM(require_prop_types());
var import_jsx_runtime7 = __toESM(require_jsx_runtime());
function TreeItem2Icon(props) {
  const {
    slots,
    slotProps,
    status
  } = props;
  const context = useTreeViewContext();
  const contextIcons = _extends({}, context.icons.slots, {
    expandIcon: context.icons.slots.expandIcon ?? TreeViewExpandIcon,
    collapseIcon: context.icons.slots.collapseIcon ?? TreeViewCollapseIcon
  });
  const contextIconProps = context.icons.slotProps;
  let iconName;
  if (slots == null ? void 0 : slots.icon) {
    iconName = "icon";
  } else if (status.expandable) {
    if (status.expanded) {
      iconName = "collapseIcon";
    } else {
      iconName = "expandIcon";
    }
  } else {
    iconName = "endIcon";
  }
  const Icon = (slots == null ? void 0 : slots[iconName]) ?? contextIcons[iconName];
  const iconProps = useSlotProps({
    elementType: Icon,
    externalSlotProps: (tempOwnerState) => _extends({}, resolveComponentProps(contextIconProps[iconName], tempOwnerState), resolveComponentProps(slotProps == null ? void 0 : slotProps[iconName], tempOwnerState)),
    // TODO: Add proper ownerState
    ownerState: {}
  });
  if (!Icon) {
    return null;
  }
  return (0, import_jsx_runtime7.jsx)(Icon, _extends({}, iconProps));
}
true ? TreeItem2Icon.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types5.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types5.default.object,
  status: import_prop_types5.default.shape({
    disabled: import_prop_types5.default.bool.isRequired,
    expandable: import_prop_types5.default.bool.isRequired,
    expanded: import_prop_types5.default.bool.isRequired,
    focused: import_prop_types5.default.bool.isRequired,
    selected: import_prop_types5.default.bool.isRequired
  }).isRequired
} : void 0;

// node_modules/@mui/x-tree-view/TreeItem2/TreeItem2.js
var import_jsx_runtime8 = __toESM(require_jsx_runtime());
var import_jsx_runtime9 = __toESM(require_jsx_runtime());
var _excluded2 = ["id", "itemId", "label", "disabled", "children", "slots", "slotProps"];
var TreeItem2Root = styled_default("li", {
  name: "MuiTreeItem2",
  slot: "Root",
  overridesResolver: (props, styles) => styles.root
})({
  listStyle: "none",
  margin: 0,
  padding: 0,
  outline: 0
});
var TreeItem2Content = styled_default("div", {
  name: "MuiTreeItem2",
  slot: "Content",
  overridesResolver: (props, styles) => styles.content,
  shouldForwardProp: (prop) => shouldForwardProp(prop) && prop !== "status"
})(({
  theme
}) => ({
  padding: theme.spacing(0.5, 1),
  borderRadius: theme.shape.borderRadius,
  width: "100%",
  boxSizing: "border-box",
  // prevent width + padding to overflow
  display: "flex",
  alignItems: "center",
  gap: theme.spacing(1),
  cursor: "pointer",
  WebkitTapHighlightColor: "transparent",
  "&:hover": {
    backgroundColor: (theme.vars || theme).palette.action.hover,
    // Reset on touch devices, it doesn't add specificity
    "@media (hover: none)": {
      backgroundColor: "transparent"
    }
  },
  [`& .${treeItemClasses.groupTransition}`]: {
    margin: 0,
    padding: 0,
    paddingLeft: 12
  },
  variants: [{
    props: ({
      status
    }) => status.disabled,
    style: {
      opacity: (theme.vars || theme).palette.action.disabledOpacity,
      backgroundColor: "transparent"
    }
  }, {
    props: ({
      status
    }) => status.focused,
    style: {
      backgroundColor: (theme.vars || theme).palette.action.focus
    }
  }, {
    props: ({
      status
    }) => status.selected,
    style: {
      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.selectedOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity),
      "&:hover": {
        backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.hoverOpacity}))` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.hoverOpacity),
        // Reset on touch devices, it doesn't add specificity
        "@media (hover: none)": {
          backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.selectedOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity)
        }
      }
    }
  }, {
    props: ({
      status
    }) => status.selected && status.focused,
    style: {
      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)
    }
  }]
}));
var TreeItem2Label = styled_default("div", {
  name: "MuiTreeItem2",
  slot: "Label",
  overridesResolver: (props, styles) => styles.label
})(({
  theme
}) => _extends({
  width: "100%",
  boxSizing: "border-box",
  // prevent width + padding to overflow
  // fixes overflow - see https://github.com/mui/material-ui/issues/27372
  minWidth: 0,
  position: "relative"
}, theme.typography.body1));
var TreeItem2IconContainer = styled_default("div", {
  name: "MuiTreeItem2",
  slot: "IconContainer",
  overridesResolver: (props, styles) => styles.iconContainer
})({
  width: 16,
  display: "flex",
  flexShrink: 0,
  justifyContent: "center",
  "& svg": {
    fontSize: 18
  }
});
var TreeItem2GroupTransition = styled_default(Collapse_default, {
  name: "MuiTreeItem2GroupTransition",
  slot: "GroupTransition",
  overridesResolver: (props, styles) => styles.groupTransition
})({
  margin: 0,
  padding: 0,
  paddingLeft: 12
});
var useUtilityClasses4 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"],
    content: ["content"],
    expanded: ["expanded"],
    selected: ["selected"],
    focused: ["focused"],
    disabled: ["disabled"],
    iconContainer: ["iconContainer"],
    label: ["label"],
    groupTransition: ["groupTransition"]
  };
  return composeClasses(slots, getTreeItemUtilityClass, classes);
};
var TreeItem2 = React18.forwardRef(function TreeItem22(inProps, forwardedRef) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiTreeItem2"
  });
  const {
    id,
    itemId,
    label,
    disabled,
    children,
    slots = {},
    slotProps = {}
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded2);
  const {
    getRootProps,
    getContentProps,
    getIconContainerProps,
    getLabelProps,
    getGroupTransitionProps,
    status
  } = useTreeItem2({
    id,
    itemId,
    children,
    label,
    disabled
  });
  const ownerState = _extends({}, props, status);
  const classes = useUtilityClasses4(ownerState);
  const Root = slots.root ?? TreeItem2Root;
  const rootProps = useSlotProps({
    elementType: Root,
    getSlotProps: getRootProps,
    externalForwardedProps: other,
    externalSlotProps: slotProps.root,
    additionalProps: {
      ref: forwardedRef
    },
    ownerState: {},
    className: classes.root
  });
  const Content = slots.content ?? TreeItem2Content;
  const contentProps = useSlotProps({
    elementType: Content,
    getSlotProps: getContentProps,
    externalSlotProps: slotProps.content,
    ownerState: {},
    className: clsx_default(classes.content, status.expanded && classes.expanded, status.selected && classes.selected, status.focused && classes.focused, status.disabled && classes.disabled)
  });
  const IconContainer = slots.iconContainer ?? TreeItem2IconContainer;
  const iconContainerProps = useSlotProps({
    elementType: IconContainer,
    getSlotProps: getIconContainerProps,
    externalSlotProps: slotProps.iconContainer,
    ownerState: {},
    className: classes.iconContainer
  });
  const Label = slots.label ?? TreeItem2Label;
  const labelProps = useSlotProps({
    elementType: Label,
    getSlotProps: getLabelProps,
    externalSlotProps: slotProps.label,
    ownerState: {},
    className: classes.label
  });
  const GroupTransition = slots.groupTransition ?? void 0;
  const groupTransitionProps = useSlotProps({
    elementType: GroupTransition,
    getSlotProps: getGroupTransitionProps,
    externalSlotProps: slotProps.groupTransition,
    ownerState: {},
    className: classes.groupTransition
  });
  return (0, import_jsx_runtime8.jsx)(TreeItem2Provider, {
    itemId,
    children: (0, import_jsx_runtime9.jsxs)(Root, _extends({}, rootProps, {
      children: [(0, import_jsx_runtime9.jsxs)(Content, _extends({}, contentProps, {
        children: [(0, import_jsx_runtime8.jsx)(IconContainer, _extends({}, iconContainerProps, {
          children: (0, import_jsx_runtime8.jsx)(TreeItem2Icon, {
            status,
            slots,
            slotProps
          })
        })), (0, import_jsx_runtime8.jsx)(Label, _extends({}, labelProps))]
      })), children && (0, import_jsx_runtime8.jsx)(TreeItem2GroupTransition, _extends({
        as: GroupTransition
      }, groupTransitionProps))]
    }))
  });
});
true ? TreeItem2.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The content of the component.
   */
  children: import_prop_types6.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types6.default.object,
  className: import_prop_types6.default.string,
  /**
   * If `true`, the item is disabled.
   * @default false
   */
  disabled: import_prop_types6.default.bool,
  /**
   * The id attribute of the item. If not provided, it will be generated.
   */
  id: import_prop_types6.default.string,
  /**
   * The id of the item.
   * Must be unique.
   */
  itemId: import_prop_types6.default.string.isRequired,
  /**
   * The label of the item.
   */
  label: import_prop_types6.default.node,
  /**
   * This prop isn't supported.
   * Use the `onItemFocus` callback on the tree if you need to monitor a item's focus.
   */
  onFocus: unsupportedProp,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types6.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types6.default.object
} : void 0;

// node_modules/@mui/x-tree-view/hooks/useTreeViewApiRef.js
var React19 = __toESM(require_react());
var useTreeViewApiRef = () => React19.useRef(void 0);
export {
  RichTreeView,
  RichTreeViewRoot,
  SimpleTreeView,
  SimpleTreeViewRoot,
  TreeItem,
  TreeItem2,
  TreeItem2Content,
  TreeItem2GroupTransition,
  TreeItem2Icon,
  TreeItem2IconContainer,
  TreeItem2Label,
  TreeItem2Provider,
  TreeItem2Root,
  TreeItemContent,
  TreeView,
  TreeViewCollapseIcon,
  TreeViewExpandIcon,
  getRichTreeViewUtilityClass,
  getSimpleTreeViewUtilityClass,
  getTreeItemUtilityClass,
  getTreeViewUtilityClass,
  richTreeViewClasses,
  simpleTreeViewClasses,
  treeItemClasses,
  treeViewClasses,
  unstable_resetCleanupTracking,
  useTreeItem2 as unstable_useTreeItem2,
  useTreeItem2Utils,
  useTreeItemState,
  useTreeViewApiRef
};
/*! Bundled license information:

@mui/x-tree-view/index.js:
  (**
   * @mui/x-tree-view v7.3.0
   *
   * @license MIT
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=@mui_x-tree-view.js.map
