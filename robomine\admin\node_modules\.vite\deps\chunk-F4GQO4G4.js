import {
  require_xojo
} from "./chunk-YBIYJ5GB.js";
import {
  require_xquery
} from "./chunk-N2JBSTLY.js";
import {
  require_yang
} from "./chunk-X5JVDQ6W.js";
import {
  require_zig
} from "./chunk-AX467A7C.js";
import {
  require_core
} from "./chunk-HNQE3F3F.js";
import {
  require_warpscript
} from "./chunk-5SPIPVKQ.js";
import {
  require_wasm
} from "./chunk-COPWWVIA.js";
import {
  require_web_idl
} from "./chunk-PZCC5XV6.js";
import {
  require_wiki
} from "./chunk-TZHGZYYJ.js";
import {
  require_wolfram
} from "./chunk-LNWXAVFK.js";
import {
  require_wren
} from "./chunk-FTU7QQLT.js";
import {
  require_xeora
} from "./chunk-EYF7Q7HZ.js";
import {
  require_xml_doc
} from "./chunk-4HKC3OPU.js";
import {
  require_uri
} from "./chunk-4R6JNAIX.js";
import {
  require_v
} from "./chunk-ZPPMCBNN.js";
import {
  require_vala
} from "./chunk-YXPA7PX6.js";
import {
  require_velocity
} from "./chunk-WTONSCHZ.js";
import {
  require_verilog
} from "./chunk-KYXHGMDS.js";
import {
  require_vhdl
} from "./chunk-VXWF4J37.js";
import {
  require_vim
} from "./chunk-VM5QFZ4C.js";
import {
  require_visual_basic
} from "./chunk-4GU6FXVG.js";
import {
  require_toml
} from "./chunk-GEBOYRDF.js";
import {
  require_tremor
} from "./chunk-MQ3TVZAI.js";
import {
  require_tsx
} from "./chunk-FYO2EMWO.js";
import {
  require_tt2
} from "./chunk-RSTFXE2D.js";
import {
  require_twig
} from "./chunk-ZMWC3GSF.js";
import {
  require_typoscript
} from "./chunk-EZI2DWNN.js";
import {
  require_unrealscript
} from "./chunk-AZNXKHBA.js";
import {
  require_uorazor
} from "./chunk-5BN4RLZW.js";
import {
  require_t4_cs
} from "./chunk-SIC7GOYS.js";
import {
  require_t4_vb
} from "./chunk-JPOY7Y3P.js";
import {
  require_t4_templating
} from "./chunk-DBVVGR3T.js";
import {
  require_vbnet
} from "./chunk-APJBDN5E.js";
import {
  require_tap
} from "./chunk-FGMZM2HG.js";
import {
  require_yaml
} from "./chunk-3XRIFGIQ.js";
import {
  require_tcl
} from "./chunk-3SMJSJMP.js";
import {
  require_textile
} from "./chunk-LH32WRWD.js";
import {
  require_sparql
} from "./chunk-2COF7G7T.js";
import {
  require_splunk_spl
} from "./chunk-VSBMF2YX.js";
import {
  require_sqf
} from "./chunk-5QJWYTBL.js";
import {
  require_squirrel
} from "./chunk-S3SK7XLD.js";
import {
  require_stan
} from "./chunk-ZPRNWYZU.js";
import {
  require_stylus
} from "./chunk-I74CVWMV.js";
import {
  require_swift
} from "./chunk-SDXL23G4.js";
import {
  require_systemd
} from "./chunk-WV26MO35.js";
import {
  require_smali
} from "./chunk-7M22UPZH.js";
import {
  require_smalltalk
} from "./chunk-MJU4PLO3.js";
import {
  require_smarty
} from "./chunk-YEZJ3EW7.js";
import {
  require_sml
} from "./chunk-E7S4OWB6.js";
import {
  require_solidity
} from "./chunk-PSELFWTN.js";
import {
  require_solution_file
} from "./chunk-NODUWYGP.js";
import {
  require_soy
} from "./chunk-SIHR4LG5.js";
import {
  require_turtle
} from "./chunk-T2KJ6VGE.js";
import {
  require_roboconf
} from "./chunk-WG525G4H.js";
import {
  require_robotframework
} from "./chunk-BSOUCSM4.js";
import {
  require_rust
} from "./chunk-VMQRFFPB.js";
import {
  require_sas
} from "./chunk-CYRVI2CU.js";
import {
  require_sass
} from "./chunk-ZJU2CTES.js";
import {
  require_scala
} from "./chunk-NMC6CJDN.js";
import {
  require_scss
} from "./chunk-PTED6MKC.js";
import {
  require_shell_session
} from "./chunk-AHEBU764.js";
import {
  require_r
} from "./chunk-GBYHJS7Q.js";
import {
  require_racket
} from "./chunk-4Y3OP4M2.js";
import {
  require_reason
} from "./chunk-OXGJSLSI.js";
import {
  require_regex
} from "./chunk-CBDCFLUJ.js";
import {
  require_rego
} from "./chunk-X5RT4DQ3.js";
import {
  require_renpy
} from "./chunk-TK3JXIAU.js";
import {
  require_rest
} from "./chunk-5NPZ7YPO.js";
import {
  require_rip
} from "./chunk-NQSZZNR6.js";
import {
  require_pure
} from "./chunk-LGW6VYOZ.js";
import {
  require_purebasic
} from "./chunk-OVDRY75A.js";
import {
  require_purescript
} from "./chunk-KDAEISAK.js";
import {
  require_python
} from "./chunk-TA4BM4M3.js";
import {
  require_q
} from "./chunk-Y4EC7R5G.js";
import {
  require_qml
} from "./chunk-DNSUNOZX.js";
import {
  require_qore
} from "./chunk-3YWTBHGM.js";
import {
  require_qsharp
} from "./chunk-E26QE22F.js";
import {
  require_processing
} from "./chunk-SESKRJ7O.js";
import {
  require_prolog
} from "./chunk-LJKKBOLQ.js";
import {
  require_promql
} from "./chunk-US37TOET.js";
import {
  require_properties
} from "./chunk-XHIWMIFE.js";
import {
  require_protobuf
} from "./chunk-QTKD4QCR.js";
import {
  require_psl
} from "./chunk-MB7HEOJW.js";
import {
  require_pug
} from "./chunk-F3L2HW5N.js";
import {
  require_puppet
} from "./chunk-GZCKQ77C.js";
import {
  require_pcaxis
} from "./chunk-C2BVBOKU.js";
import {
  require_peoplecode
} from "./chunk-HYQSSOPX.js";
import {
  require_perl
} from "./chunk-7WJFPEUB.js";
import {
  require_php_extras
} from "./chunk-M65XCNRQ.js";
import {
  require_phpdoc
} from "./chunk-5IGIQDN7.js";
import {
  require_plsql
} from "./chunk-T6DUX2N4.js";
import {
  require_powerquery
} from "./chunk-GN7YGVJV.js";
import {
  require_powershell
} from "./chunk-GY7ENNJB.js";
import {
  require_ocaml
} from "./chunk-7KWLRCPJ.js";
import {
  require_opencl
} from "./chunk-6C6ELDMF.js";
import {
  require_openqasm
} from "./chunk-ZW7GYEQH.js";
import {
  require_oz
} from "./chunk-HK54UUUG.js";
import {
  require_parigp
} from "./chunk-6VGQMVEU.js";
import {
  require_parser
} from "./chunk-BETA74G3.js";
import {
  require_pascal
} from "./chunk-UQQP6HTD.js";
import {
  require_pascaligo
} from "./chunk-HCJHBG5X.js";
import {
  require_nasm
} from "./chunk-K2NM6I4B.js";
import {
  require_neon
} from "./chunk-ZEXCOQT4.js";
import {
  require_nevod
} from "./chunk-33SMBHEB.js";
import {
  require_nginx
} from "./chunk-QMZL5MY2.js";
import {
  require_nim
} from "./chunk-MACTLZL5.js";
import {
  require_nix
} from "./chunk-4PDZZ7EO.js";
import {
  require_nsis
} from "./chunk-OHATDML6.js";
import {
  require_objectivec
} from "./chunk-ADVTSA4M.js";
import {
  require_mizar
} from "./chunk-VHOFPLFN.js";
import {
  require_mongodb
} from "./chunk-AVA7R5IF.js";
import {
  require_monkey
} from "./chunk-2F5XTKCS.js";
import {
  require_moonscript
} from "./chunk-NROMO3A5.js";
import {
  require_n1ql
} from "./chunk-AAAPVWLB.js";
import {
  require_n4js
} from "./chunk-VE52LAFV.js";
import {
  require_nand2tetris_hdl
} from "./chunk-IDGSHTL7.js";
import {
  require_naniscript
} from "./chunk-7BIFYOMA.js";
import {
  require_magma
} from "./chunk-VG3YTU4I.js";
import {
  require_makefile
} from "./chunk-IQTUIJGR.js";
import {
  require_markdown
} from "./chunk-4P24RYI6.js";
import {
  require_matlab
} from "./chunk-VTQWTQEY.js";
import {
  require_maxscript
} from "./chunk-GJDW3QON.js";
import {
  require_mel
} from "./chunk-7TUZ7OMV.js";
import {
  require_mermaid
} from "./chunk-RRMH7WIN.js";
import {
  require_lilypond
} from "./chunk-4A2TXZ55.js";
import {
  require_scheme
} from "./chunk-KGOEVPZL.js";
import {
  require_liquid
} from "./chunk-D4EDNONV.js";
import {
  require_lisp
} from "./chunk-DSNCNISI.js";
import {
  require_livescript
} from "./chunk-GMFASVIY.js";
import {
  require_llvm
} from "./chunk-C5QVILV3.js";
import {
  require_log
} from "./chunk-KA6AB3BN.js";
import {
  require_lolcode
} from "./chunk-6YZZ2EGD.js";
import {
  require_keyman
} from "./chunk-NWEJTLKE.js";
import {
  require_kotlin
} from "./chunk-5DYBR7SW.js";
import {
  require_kumir
} from "./chunk-7QGGBMJZ.js";
import {
  require_kusto
} from "./chunk-PQJFCFHA.js";
import {
  require_latex
} from "./chunk-LFBCXIP4.js";
import {
  require_latte
} from "./chunk-FBECEE37.js";
import {
  require_php
} from "./chunk-KHWMYH6F.js";
import {
  require_less
} from "./chunk-V5XBDL4H.js";
import {
  require_jsdoc
} from "./chunk-KKCFVGMB.js";
import {
  require_json5
} from "./chunk-FBMSPOF4.js";
import {
  require_jsonp
} from "./chunk-SFUOBGLJ.js";
import {
  require_json
} from "./chunk-GBORVQKY.js";
import {
  require_jsstacktrace
} from "./chunk-W2JHV4B7.js";
import {
  require_jsx
} from "./chunk-SYDAPH5J.js";
import {
  require_julia
} from "./chunk-4XNRB7KD.js";
import {
  require_keepalived
} from "./chunk-UODTANG5.js";
import {
  require_javastacktrace
} from "./chunk-Y2BGVDXI.js";
import {
  require_jexl
} from "./chunk-MQZDMDJY.js";
import {
  require_jolie
} from "./chunk-7OVMHUJJ.js";
import {
  require_jq
} from "./chunk-OOCX5XW5.js";
import {
  require_js_extras
} from "./chunk-ZT4XJHR7.js";
import {
  require_js_templates
} from "./chunk-6ZLAI4A7.js";
import {
  require_typescript
} from "./chunk-4C24NOVJ.js";
import {
  require_ignore
} from "./chunk-L4UV5WXS.js";
import {
  require_inform7
} from "./chunk-6XZVRMBK.js";
import {
  require_ini
} from "./chunk-3UXARAL6.js";
import {
  require_io
} from "./chunk-R6CW6O2Z.js";
import {
  require_j
} from "./chunk-L4ZD3PEP.js";
import {
  require_javadoc
} from "./chunk-E44SQ7SL.js";
import {
  require_java
} from "./chunk-MS4WUD7R.js";
import {
  require_javadoclike
} from "./chunk-HQRJ4YVC.js";
import {
  require_hpkp
} from "./chunk-UOAYVIQG.js";
import {
  require_hsts
} from "./chunk-VMQ2MD7F.js";
import {
  require_http
} from "./chunk-AUJMKHCU.js";
import {
  require_ichigojam
} from "./chunk-TWYMZQZH.js";
import {
  require_icon
} from "./chunk-AKJ6FVBU.js";
import {
  require_icu_message_format
} from "./chunk-CEKFDHWD.js";
import {
  require_idris
} from "./chunk-53GOMQVL.js";
import {
  require_iecst
} from "./chunk-6L6HGCRK.js";
import {
  require_groovy
} from "./chunk-UT3NOAQO.js";
import {
  require_haml
} from "./chunk-UVTNGGAM.js";
import {
  require_handlebars
} from "./chunk-RAKGCCCB.js";
import {
  require_haskell
} from "./chunk-P72ZOGQE.js";
import {
  require_haxe
} from "./chunk-Y7GLLYGS.js";
import {
  require_hcl
} from "./chunk-DAD4FKFB.js";
import {
  require_hlsl
} from "./chunk-NBTZWFEQ.js";
import {
  require_hoon
} from "./chunk-ZQKONFNH.js";
import {
  require_gherkin
} from "./chunk-JK324FNY.js";
import {
  require_git
} from "./chunk-WUPXKDUW.js";
import {
  require_glsl
} from "./chunk-P7VQK4GY.js";
import {
  require_gml
} from "./chunk-6VZMXILT.js";
import {
  require_gn
} from "./chunk-54FDEJOW.js";
import {
  require_go_module
} from "./chunk-ZDUCES3E.js";
import {
  require_go
} from "./chunk-QG5AS5D7.js";
import {
  require_graphql
} from "./chunk-EDXOXTDB.js";
import {
  require_flow
} from "./chunk-ZR3JHVT5.js";
import {
  require_fortran
} from "./chunk-GW2V4EY4.js";
import {
  require_fsharp
} from "./chunk-BISVHLGD.js";
import {
  require_ftl
} from "./chunk-I5RZYSAQ.js";
import {
  require_gap
} from "./chunk-UFMY5LOP.js";
import {
  require_gcode
} from "./chunk-5JYWK77E.js";
import {
  require_gdscript
} from "./chunk-NZCJ5AT5.js";
import {
  require_gedcom
} from "./chunk-DGIL7ZQF.js";
import {
  require_erb
} from "./chunk-FR2B7FQD.js";
import {
  require_erlang
} from "./chunk-IG4OOWB5.js";
import {
  require_etlua
} from "./chunk-KMULB26W.js";
import {
  require_lua
} from "./chunk-2INOD72V.js";
import {
  require_excel_formula
} from "./chunk-SMNWKMQP.js";
import {
  require_factor
} from "./chunk-Z7ILR4Z2.js";
import {
  require_false
} from "./chunk-D5MOGFI7.js";
import {
  require_firestore_security_rules
} from "./chunk-BI6OHBWH.js";
import {
  require_docker
} from "./chunk-SY6K7RL4.js";
import {
  require_dot
} from "./chunk-4AU36UKC.js";
import {
  require_ebnf
} from "./chunk-QAZJ3IP6.js";
import {
  require_editorconfig
} from "./chunk-56YG4C74.js";
import {
  require_eiffel
} from "./chunk-WESDAM7Z.js";
import {
  require_ejs
} from "./chunk-3HCMEWLQ.js";
import {
  require_elixir
} from "./chunk-QYKN5Q75.js";
import {
  require_elm
} from "./chunk-G4MSHABD.js";
import {
  require_dart
} from "./chunk-S6PKT7TK.js";
import {
  require_dataweave
} from "./chunk-5K66YILU.js";
import {
  require_dax
} from "./chunk-WEJ2PUSL.js";
import {
  require_dhall
} from "./chunk-XQJ4TR4L.js";
import {
  require_diff
} from "./chunk-ULYWPFJU.js";
import {
  require_django
} from "./chunk-5DHFIQFO.js";
import {
  require_markup_templating
} from "./chunk-A4ZJNXVC.js";
import {
  require_dns_zone_file
} from "./chunk-OF7LYFNO.js";
import {
  require_crystal
} from "./chunk-TYTQKTKY.js";
import {
  require_cshtml
} from "./chunk-JDQBIA2R.js";
import {
  require_csp
} from "./chunk-GZAKYDEG.js";
import {
  require_css_extras
} from "./chunk-TAWKKMD5.js";
import {
  require_csv
} from "./chunk-H3US5XS3.js";
import {
  require_cypher
} from "./chunk-WTECNCGV.js";
import {
  require_d
} from "./chunk-M5LGKY7F.js";
import {
  require_clojure
} from "./chunk-Q5DVBBPW.js";
import {
  require_cmake
} from "./chunk-VN6QLV7W.js";
import {
  require_cobol
} from "./chunk-IGFUJBG3.js";
import {
  require_coffeescript
} from "./chunk-F3P4YHN6.js";
import {
  require_concurnas
} from "./chunk-ZELKZNHG.js";
import {
  require_coq
} from "./chunk-SZW7V66G.js";
import {
  require_ruby
} from "./chunk-Y7PWGO3G.js";
import {
  require_bnf
} from "./chunk-AIQZFL3W.js";
import {
  require_brainfuck
} from "./chunk-BYARBT3J.js";
import {
  require_brightscript
} from "./chunk-UEAMEGGE.js";
import {
  require_bro
} from "./chunk-T76VSZHI.js";
import {
  require_bsl
} from "./chunk-6ZDABPXP.js";
import {
  require_cfscript
} from "./chunk-MBYH3DF6.js";
import {
  require_chaiscript
} from "./chunk-OFAJVJNB.js";
import {
  require_cil
} from "./chunk-CG4OATLW.js";
import {
  require_avro_idl
} from "./chunk-4MWM6H75.js";
import {
  require_bash
} from "./chunk-VQINAXZH.js";
import {
  require_basic
} from "./chunk-6LUCFZCW.js";
import {
  require_batch
} from "./chunk-BGHSABGB.js";
import {
  require_bbcode
} from "./chunk-VOHKWFVX.js";
import {
  require_bicep
} from "./chunk-6OETGL7G.js";
import {
  require_birb
} from "./chunk-U2M5FGJ3.js";
import {
  require_bison
} from "./chunk-U2ORALU3.js";
import {
  require_asciidoc
} from "./chunk-XVY6SCVP.js";
import {
  require_asm6502
} from "./chunk-ZDA6EMLW.js";
import {
  require_asmatmel
} from "./chunk-M5P7BDVK.js";
import {
  require_aspnet
} from "./chunk-P7RSL57I.js";
import {
  require_csharp
} from "./chunk-7N4QIYI3.js";
import {
  require_autohotkey
} from "./chunk-P75NFXZK.js";
import {
  require_autoit
} from "./chunk-SG4UYVN4.js";
import {
  require_avisynth
} from "./chunk-IJ3NZRBK.js";
import {
  require_apex
} from "./chunk-GVUL2FUM.js";
import {
  require_apl
} from "./chunk-WILBN7AK.js";
import {
  require_applescript
} from "./chunk-W2DHHAE7.js";
import {
  require_aql
} from "./chunk-W7UEEP5W.js";
import {
  require_arduino
} from "./chunk-KUC2YRR7.js";
import {
  require_cpp
} from "./chunk-EYUPFCVU.js";
import {
  require_c
} from "./chunk-HYK7ILMH.js";
import {
  require_arff
} from "./chunk-VOWIN5PE.js";
import {
  require_abnf
} from "./chunk-RJELC7QE.js";
import {
  require_actionscript
} from "./chunk-NEODWWKT.js";
import {
  require_ada
} from "./chunk-HXSZLT3J.js";
import {
  require_agda
} from "./chunk-DDVNSNEU.js";
import {
  require_al
} from "./chunk-SEEN7WGQ.js";
import {
  require_antlr4
} from "./chunk-LYEL7N2F.js";
import {
  require_apacheconf
} from "./chunk-B6OHXXFE.js";
import {
  require_sql
} from "./chunk-HFET2M5D.js";
import {
  require_abap
} from "./chunk-U7WY5ZPK.js";
import {
  __commonJS
} from "./chunk-BYPFWIQ6.js";

// node_modules/refractor/index.js
var require_refractor = __commonJS({
  "node_modules/refractor/index.js"(exports, module) {
    var refractor = require_core();
    module.exports = refractor;
    refractor.register(require_abap());
    refractor.register(require_abnf());
    refractor.register(require_actionscript());
    refractor.register(require_ada());
    refractor.register(require_agda());
    refractor.register(require_al());
    refractor.register(require_antlr4());
    refractor.register(require_apacheconf());
    refractor.register(require_apex());
    refractor.register(require_apl());
    refractor.register(require_applescript());
    refractor.register(require_aql());
    refractor.register(require_arduino());
    refractor.register(require_arff());
    refractor.register(require_asciidoc());
    refractor.register(require_asm6502());
    refractor.register(require_asmatmel());
    refractor.register(require_aspnet());
    refractor.register(require_autohotkey());
    refractor.register(require_autoit());
    refractor.register(require_avisynth());
    refractor.register(require_avro_idl());
    refractor.register(require_bash());
    refractor.register(require_basic());
    refractor.register(require_batch());
    refractor.register(require_bbcode());
    refractor.register(require_bicep());
    refractor.register(require_birb());
    refractor.register(require_bison());
    refractor.register(require_bnf());
    refractor.register(require_brainfuck());
    refractor.register(require_brightscript());
    refractor.register(require_bro());
    refractor.register(require_bsl());
    refractor.register(require_c());
    refractor.register(require_cfscript());
    refractor.register(require_chaiscript());
    refractor.register(require_cil());
    refractor.register(require_clojure());
    refractor.register(require_cmake());
    refractor.register(require_cobol());
    refractor.register(require_coffeescript());
    refractor.register(require_concurnas());
    refractor.register(require_coq());
    refractor.register(require_cpp());
    refractor.register(require_crystal());
    refractor.register(require_csharp());
    refractor.register(require_cshtml());
    refractor.register(require_csp());
    refractor.register(require_css_extras());
    refractor.register(require_csv());
    refractor.register(require_cypher());
    refractor.register(require_d());
    refractor.register(require_dart());
    refractor.register(require_dataweave());
    refractor.register(require_dax());
    refractor.register(require_dhall());
    refractor.register(require_diff());
    refractor.register(require_django());
    refractor.register(require_dns_zone_file());
    refractor.register(require_docker());
    refractor.register(require_dot());
    refractor.register(require_ebnf());
    refractor.register(require_editorconfig());
    refractor.register(require_eiffel());
    refractor.register(require_ejs());
    refractor.register(require_elixir());
    refractor.register(require_elm());
    refractor.register(require_erb());
    refractor.register(require_erlang());
    refractor.register(require_etlua());
    refractor.register(require_excel_formula());
    refractor.register(require_factor());
    refractor.register(require_false());
    refractor.register(require_firestore_security_rules());
    refractor.register(require_flow());
    refractor.register(require_fortran());
    refractor.register(require_fsharp());
    refractor.register(require_ftl());
    refractor.register(require_gap());
    refractor.register(require_gcode());
    refractor.register(require_gdscript());
    refractor.register(require_gedcom());
    refractor.register(require_gherkin());
    refractor.register(require_git());
    refractor.register(require_glsl());
    refractor.register(require_gml());
    refractor.register(require_gn());
    refractor.register(require_go_module());
    refractor.register(require_go());
    refractor.register(require_graphql());
    refractor.register(require_groovy());
    refractor.register(require_haml());
    refractor.register(require_handlebars());
    refractor.register(require_haskell());
    refractor.register(require_haxe());
    refractor.register(require_hcl());
    refractor.register(require_hlsl());
    refractor.register(require_hoon());
    refractor.register(require_hpkp());
    refractor.register(require_hsts());
    refractor.register(require_http());
    refractor.register(require_ichigojam());
    refractor.register(require_icon());
    refractor.register(require_icu_message_format());
    refractor.register(require_idris());
    refractor.register(require_iecst());
    refractor.register(require_ignore());
    refractor.register(require_inform7());
    refractor.register(require_ini());
    refractor.register(require_io());
    refractor.register(require_j());
    refractor.register(require_java());
    refractor.register(require_javadoc());
    refractor.register(require_javadoclike());
    refractor.register(require_javastacktrace());
    refractor.register(require_jexl());
    refractor.register(require_jolie());
    refractor.register(require_jq());
    refractor.register(require_js_extras());
    refractor.register(require_js_templates());
    refractor.register(require_jsdoc());
    refractor.register(require_json());
    refractor.register(require_json5());
    refractor.register(require_jsonp());
    refractor.register(require_jsstacktrace());
    refractor.register(require_jsx());
    refractor.register(require_julia());
    refractor.register(require_keepalived());
    refractor.register(require_keyman());
    refractor.register(require_kotlin());
    refractor.register(require_kumir());
    refractor.register(require_kusto());
    refractor.register(require_latex());
    refractor.register(require_latte());
    refractor.register(require_less());
    refractor.register(require_lilypond());
    refractor.register(require_liquid());
    refractor.register(require_lisp());
    refractor.register(require_livescript());
    refractor.register(require_llvm());
    refractor.register(require_log());
    refractor.register(require_lolcode());
    refractor.register(require_lua());
    refractor.register(require_magma());
    refractor.register(require_makefile());
    refractor.register(require_markdown());
    refractor.register(require_markup_templating());
    refractor.register(require_matlab());
    refractor.register(require_maxscript());
    refractor.register(require_mel());
    refractor.register(require_mermaid());
    refractor.register(require_mizar());
    refractor.register(require_mongodb());
    refractor.register(require_monkey());
    refractor.register(require_moonscript());
    refractor.register(require_n1ql());
    refractor.register(require_n4js());
    refractor.register(require_nand2tetris_hdl());
    refractor.register(require_naniscript());
    refractor.register(require_nasm());
    refractor.register(require_neon());
    refractor.register(require_nevod());
    refractor.register(require_nginx());
    refractor.register(require_nim());
    refractor.register(require_nix());
    refractor.register(require_nsis());
    refractor.register(require_objectivec());
    refractor.register(require_ocaml());
    refractor.register(require_opencl());
    refractor.register(require_openqasm());
    refractor.register(require_oz());
    refractor.register(require_parigp());
    refractor.register(require_parser());
    refractor.register(require_pascal());
    refractor.register(require_pascaligo());
    refractor.register(require_pcaxis());
    refractor.register(require_peoplecode());
    refractor.register(require_perl());
    refractor.register(require_php_extras());
    refractor.register(require_php());
    refractor.register(require_phpdoc());
    refractor.register(require_plsql());
    refractor.register(require_powerquery());
    refractor.register(require_powershell());
    refractor.register(require_processing());
    refractor.register(require_prolog());
    refractor.register(require_promql());
    refractor.register(require_properties());
    refractor.register(require_protobuf());
    refractor.register(require_psl());
    refractor.register(require_pug());
    refractor.register(require_puppet());
    refractor.register(require_pure());
    refractor.register(require_purebasic());
    refractor.register(require_purescript());
    refractor.register(require_python());
    refractor.register(require_q());
    refractor.register(require_qml());
    refractor.register(require_qore());
    refractor.register(require_qsharp());
    refractor.register(require_r());
    refractor.register(require_racket());
    refractor.register(require_reason());
    refractor.register(require_regex());
    refractor.register(require_rego());
    refractor.register(require_renpy());
    refractor.register(require_rest());
    refractor.register(require_rip());
    refractor.register(require_roboconf());
    refractor.register(require_robotframework());
    refractor.register(require_ruby());
    refractor.register(require_rust());
    refractor.register(require_sas());
    refractor.register(require_sass());
    refractor.register(require_scala());
    refractor.register(require_scheme());
    refractor.register(require_scss());
    refractor.register(require_shell_session());
    refractor.register(require_smali());
    refractor.register(require_smalltalk());
    refractor.register(require_smarty());
    refractor.register(require_sml());
    refractor.register(require_solidity());
    refractor.register(require_solution_file());
    refractor.register(require_soy());
    refractor.register(require_sparql());
    refractor.register(require_splunk_spl());
    refractor.register(require_sqf());
    refractor.register(require_sql());
    refractor.register(require_squirrel());
    refractor.register(require_stan());
    refractor.register(require_stylus());
    refractor.register(require_swift());
    refractor.register(require_systemd());
    refractor.register(require_t4_cs());
    refractor.register(require_t4_templating());
    refractor.register(require_t4_vb());
    refractor.register(require_tap());
    refractor.register(require_tcl());
    refractor.register(require_textile());
    refractor.register(require_toml());
    refractor.register(require_tremor());
    refractor.register(require_tsx());
    refractor.register(require_tt2());
    refractor.register(require_turtle());
    refractor.register(require_twig());
    refractor.register(require_typescript());
    refractor.register(require_typoscript());
    refractor.register(require_unrealscript());
    refractor.register(require_uorazor());
    refractor.register(require_uri());
    refractor.register(require_v());
    refractor.register(require_vala());
    refractor.register(require_vbnet());
    refractor.register(require_velocity());
    refractor.register(require_verilog());
    refractor.register(require_vhdl());
    refractor.register(require_vim());
    refractor.register(require_visual_basic());
    refractor.register(require_warpscript());
    refractor.register(require_wasm());
    refractor.register(require_web_idl());
    refractor.register(require_wiki());
    refractor.register(require_wolfram());
    refractor.register(require_wren());
    refractor.register(require_xeora());
    refractor.register(require_xml_doc());
    refractor.register(require_xojo());
    refractor.register(require_xquery());
    refractor.register(require_yaml());
    refractor.register(require_yang());
    refractor.register(require_zig());
  }
});

export {
  require_refractor
};
//# sourceMappingURL=chunk-F4GQO4G4.js.map
