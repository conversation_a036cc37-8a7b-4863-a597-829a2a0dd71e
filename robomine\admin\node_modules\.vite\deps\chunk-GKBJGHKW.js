import {
  createUseThemeProps
} from "./chunk-BKVKTDH2.js";
import {
  Typography_default
} from "./chunk-P54SEAOW.js";
import {
  styled_default
} from "./chunk-KIJLS2TV.js";
import {
  clsx_default
} from "./chunk-YV3COZNF.js";
import {
  composeClasses,
  generateUtilityClass,
  generateUtilityClasses
} from "./chunk-EH52VBW6.js";
import {
  require_prop_types
} from "./chunk-MDE6ZET7.js";
import {
  _objectWithoutPropertiesLoose
} from "./chunk-OBSDRUBD.js";
import {
  require_jsx_runtime
} from "./chunk-D4DBS43D.js";
import {
  _extends,
  init_extends
} from "./chunk-4GAI7T4A.js";
import {
  require_react
} from "./chunk-R56R2YIZ.js";
import {
  __toESM
} from "./chunk-BYPFWIQ6.js";

// node_modules/@mui/material/AlertTitle/AlertTitle.js
init_extends();
var React = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());

// node_modules/@mui/material/AlertTitle/alertTitleClasses.js
function getAlertTitleUtilityClass(slot) {
  return generateUtilityClass("MuiAlertTitle", slot);
}
var alertTitleClasses = generateUtilityClasses("MuiAlertTitle", ["root"]);
var alertTitleClasses_default = alertTitleClasses;

// node_modules/@mui/material/AlertTitle/AlertTitle.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var _excluded = ["className"];
var useThemeProps = createUseThemeProps("MuiAlertTitle");
var useUtilityClasses = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"]
  };
  return composeClasses(slots, getAlertTitleUtilityClass, classes);
};
var AlertTitleRoot = styled_default(Typography_default, {
  name: "MuiAlertTitle",
  slot: "Root",
  overridesResolver: (props, styles) => styles.root
})(({
  theme
}) => {
  return {
    fontWeight: theme.typography.fontWeightMedium,
    marginTop: -2
  };
});
var AlertTitle = React.forwardRef(function AlertTitle2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiAlertTitle"
  });
  const {
    className
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded);
  const ownerState = props;
  const classes = useUtilityClasses(ownerState);
  return (0, import_jsx_runtime.jsx)(AlertTitleRoot, _extends({
    gutterBottom: true,
    component: "div",
    ownerState,
    ref,
    className: clsx_default(classes.root, className)
  }, other));
});
true ? AlertTitle.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The content of the component.
   */
  children: import_prop_types.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types.default.object,
  /**
   * @ignore
   */
  className: import_prop_types.default.string,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types.default.oneOfType([import_prop_types.default.arrayOf(import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object, import_prop_types.default.bool])), import_prop_types.default.func, import_prop_types.default.object])
} : void 0;
var AlertTitle_default = AlertTitle;

export {
  getAlertTitleUtilityClass,
  alertTitleClasses_default,
  AlertTitle_default
};
//# sourceMappingURL=chunk-GKBJGHKW.js.map
