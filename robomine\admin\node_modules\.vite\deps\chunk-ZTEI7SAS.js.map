{"version": 3, "sources": ["../../@mui/material/Skeleton/Skeleton.js", "../../@mui/material/Skeleton/skeletonClasses.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"animation\", \"className\", \"component\", \"height\", \"style\", \"variant\", \"width\"];\nlet _ = t => t,\n  _t,\n  _t2,\n  _t3,\n  _t4;\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { keyframes, css } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha, unstable_getUnit as getUnit, unstable_toUnitless as toUnitless } from '../styles';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport { getSkeletonUtilityClass } from './skeletonClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant,\n    animation,\n    hasChildren,\n    width,\n    height\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, animation, hasChildren && 'withChildren', hasChildren && !width && 'fitContent', hasChildren && !height && 'heightAuto']\n  };\n  return composeClasses(slots, getSkeletonUtilityClass, classes);\n};\nconst pulseKeyframe = keyframes(_t || (_t = _`\n  0% {\n    opacity: 1;\n  }\n\n  50% {\n    opacity: 0.4;\n  }\n\n  100% {\n    opacity: 1;\n  }\n`));\nconst waveKeyframe = keyframes(_t2 || (_t2 = _`\n  0% {\n    transform: translateX(-100%);\n  }\n\n  50% {\n    /* +0.5s of delay between each loop */\n    transform: translateX(100%);\n  }\n\n  100% {\n    transform: translateX(100%);\n  }\n`));\nconst SkeletonRoot = styled('span', {\n  name: 'MuiSkeleton',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], ownerState.animation !== false && styles[ownerState.animation], ownerState.hasChildren && styles.withChildren, ownerState.hasChildren && !ownerState.width && styles.fitContent, ownerState.hasChildren && !ownerState.height && styles.heightAuto];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  const radiusUnit = getUnit(theme.shape.borderRadius) || 'px';\n  const radiusValue = toUnitless(theme.shape.borderRadius);\n  return _extends({\n    display: 'block',\n    // Create a \"on paper\" color with sufficient contrast retaining the color\n    backgroundColor: theme.vars ? theme.vars.palette.Skeleton.bg : alpha(theme.palette.text.primary, theme.palette.mode === 'light' ? 0.11 : 0.13),\n    height: '1.2em'\n  }, ownerState.variant === 'text' && {\n    marginTop: 0,\n    marginBottom: 0,\n    height: 'auto',\n    transformOrigin: '0 55%',\n    transform: 'scale(1, 0.60)',\n    borderRadius: `${radiusValue}${radiusUnit}/${Math.round(radiusValue / 0.6 * 10) / 10}${radiusUnit}`,\n    '&:empty:before': {\n      content: '\"\\\\00a0\"'\n    }\n  }, ownerState.variant === 'circular' && {\n    borderRadius: '50%'\n  }, ownerState.variant === 'rounded' && {\n    borderRadius: (theme.vars || theme).shape.borderRadius\n  }, ownerState.hasChildren && {\n    '& > *': {\n      visibility: 'hidden'\n    }\n  }, ownerState.hasChildren && !ownerState.width && {\n    maxWidth: 'fit-content'\n  }, ownerState.hasChildren && !ownerState.height && {\n    height: 'auto'\n  });\n}, ({\n  ownerState\n}) => ownerState.animation === 'pulse' && css(_t3 || (_t3 = _`\n      animation: ${0} 2s ease-in-out 0.5s infinite;\n    `), pulseKeyframe), ({\n  ownerState,\n  theme\n}) => ownerState.animation === 'wave' && css(_t4 || (_t4 = _`\n      position: relative;\n      overflow: hidden;\n\n      /* Fix bug in Safari https://bugs.webkit.org/show_bug.cgi?id=68196 */\n      -webkit-mask-image: -webkit-radial-gradient(white, black);\n\n      &::after {\n        animation: ${0} 2s linear 0.5s infinite;\n        background: linear-gradient(\n          90deg,\n          transparent,\n          ${0},\n          transparent\n        );\n        content: '';\n        position: absolute;\n        transform: translateX(-100%); /* Avoid flash during server-side hydration */\n        bottom: 0;\n        left: 0;\n        right: 0;\n        top: 0;\n      }\n    `), waveKeyframe, (theme.vars || theme).palette.action.hover));\nconst Skeleton = /*#__PURE__*/React.forwardRef(function Skeleton(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiSkeleton'\n  });\n  const {\n      animation = 'pulse',\n      className,\n      component = 'span',\n      height,\n      style,\n      variant = 'text',\n      width\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    animation,\n    component,\n    variant,\n    hasChildren: Boolean(other.children)\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(SkeletonRoot, _extends({\n    as: component,\n    ref: ref,\n    className: clsx(classes.root, className),\n    ownerState: ownerState\n  }, other, {\n    style: _extends({\n      width,\n      height\n    }, style)\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Skeleton.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The animation.\n   * If `false` the animation effect is disabled.\n   * @default 'pulse'\n   */\n  animation: PropTypes.oneOf(['pulse', 'wave', false]),\n  /**\n   * Optional children to infer width and height from.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Height of the skeleton.\n   * Useful when you don't want to adapt the skeleton to a text element but for instance a card.\n   */\n  height: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The type of content that will be rendered.\n   * @default 'text'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['circular', 'rectangular', 'rounded', 'text']), PropTypes.string]),\n  /**\n   * Width of the skeleton.\n   * Useful when the skeleton is inside an inline element with no width of its own.\n   */\n  width: PropTypes.oneOfType([PropTypes.number, PropTypes.string])\n} : void 0;\nexport default Skeleton;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSkeletonUtilityClass(slot) {\n  return generateUtilityClass('MuiSkeleton', slot);\n}\nconst skeletonClasses = generateUtilityClasses('MuiSkeleton', ['root', 'text', 'rectangular', 'rounded', 'circular', 'pulse', 'wave', 'withChildren', 'fitContent', 'heightAuto']);\nexport default skeletonClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AAOA,YAAuB;AAEvB,wBAAsB;;;ACVf,SAAS,wBAAwB,MAAM;AAC5C,SAAO,qBAAqB,eAAe,IAAI;AACjD;AACA,IAAM,kBAAkB,uBAAuB,eAAe,CAAC,QAAQ,QAAQ,eAAe,WAAW,YAAY,SAAS,QAAQ,gBAAgB,cAAc,YAAY,CAAC;AACjL,IAAO,0BAAQ;;;ADaf,yBAA4B;AAf5B,IAAM,YAAY,CAAC,aAAa,aAAa,aAAa,UAAU,SAAS,WAAW,OAAO;AAC/F,IAAI,IAAI,OAAK;AAAb,IACE;AADF,IAEE;AAFF,IAGE;AAHF,IAIE;AAWF,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,SAAS,WAAW,eAAe,gBAAgB,eAAe,CAAC,SAAS,cAAc,eAAe,CAAC,UAAU,YAAY;AAAA,EACjJ;AACA,SAAO,eAAe,OAAO,yBAAyB,OAAO;AAC/D;AACA,IAAM,gBAAgB,UAAU,OAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAY1C;AACF,IAAM,eAAe,UAAU,QAAQ,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAa3C;AACF,IAAM,eAAe,eAAO,QAAQ;AAAA,EAClC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,OAAO,WAAW,OAAO,GAAG,WAAW,cAAc,SAAS,OAAO,WAAW,SAAS,GAAG,WAAW,eAAe,OAAO,cAAc,WAAW,eAAe,CAAC,WAAW,SAAS,OAAO,YAAY,WAAW,eAAe,CAAC,WAAW,UAAU,OAAO,UAAU;AAAA,EACrS;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AAAA,EACA;AACF,MAAM;AACJ,QAAM,aAAa,QAAQ,MAAM,MAAM,YAAY,KAAK;AACxD,QAAM,cAAc,WAAW,MAAM,MAAM,YAAY;AACvD,SAAO,SAAS;AAAA,IACd,SAAS;AAAA;AAAA,IAET,iBAAiB,MAAM,OAAO,MAAM,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM,QAAQ,KAAK,SAAS,MAAM,QAAQ,SAAS,UAAU,OAAO,IAAI;AAAA,IAC7I,QAAQ;AAAA,EACV,GAAG,WAAW,YAAY,UAAU;AAAA,IAClC,WAAW;AAAA,IACX,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,cAAc,GAAG,WAAW,GAAG,UAAU,IAAI,KAAK,MAAM,cAAc,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU;AAAA,IACjG,kBAAkB;AAAA,MAChB,SAAS;AAAA,IACX;AAAA,EACF,GAAG,WAAW,YAAY,cAAc;AAAA,IACtC,cAAc;AAAA,EAChB,GAAG,WAAW,YAAY,aAAa;AAAA,IACrC,eAAe,MAAM,QAAQ,OAAO,MAAM;AAAA,EAC5C,GAAG,WAAW,eAAe;AAAA,IAC3B,SAAS;AAAA,MACP,YAAY;AAAA,IACd;AAAA,EACF,GAAG,WAAW,eAAe,CAAC,WAAW,SAAS;AAAA,IAChD,UAAU;AAAA,EACZ,GAAG,WAAW,eAAe,CAAC,WAAW,UAAU;AAAA,IACjD,QAAQ;AAAA,EACV,CAAC;AACH,GAAG,CAAC;AAAA,EACF;AACF,MAAM,WAAW,cAAc,WAAW,IAAI,QAAQ,MAAM;AAAA,mBACzC,CAAC;AAAA,QACZ,aAAa,GAAG,CAAC;AAAA,EACvB;AAAA,EACA;AACF,MAAM,WAAW,cAAc,UAAU,IAAI,QAAQ,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAQtC,CAAC;AAAA;AAAA;AAAA;AAAA,YAIV,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAWL,eAAe,MAAM,QAAQ,OAAO,QAAQ,OAAO,KAAK,CAAC;AACjE,IAAM,WAA8B,iBAAW,SAASA,UAAS,SAAS,KAAK;AAC7E,QAAM,QAAQ,cAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF,YAAY;AAAA,IACZ;AAAA,IACA,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa,QAAQ,MAAM,QAAQ;AAAA,EACrC,CAAC;AACD,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,cAAc,SAAS;AAAA,IAC9C,IAAI;AAAA,IACJ;AAAA,IACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,EACF,GAAG,OAAO;AAAA,IACR,OAAO,SAAS;AAAA,MACd;AAAA,MACA;AAAA,IACF,GAAG,KAAK;AAAA,EACV,CAAC,CAAC;AACJ,CAAC;AACD,OAAwC,SAAS,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUlF,WAAW,kBAAAC,QAAU,MAAM,CAAC,SAAS,QAAQ,KAAK,CAAC;AAAA;AAAA;AAAA;AAAA,EAInD,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAIhE,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtJ,SAAS,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,YAAY,eAAe,WAAW,MAAM,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtJ,OAAO,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,CAAC;AACjE,IAAI;AACJ,IAAO,mBAAQ;", "names": ["Skeleton", "_jsx", "PropTypes"]}