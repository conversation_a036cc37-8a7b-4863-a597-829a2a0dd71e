import {
  inputBaseClasses_default
} from "./chunk-KKGC5RZS.js";
import {
  generateUtilityClass,
  generateUtilityClasses
} from "./chunk-EH52VBW6.js";
import {
  _extends,
  init_extends
} from "./chunk-4GAI7T4A.js";

// node_modules/@mui/material/OutlinedInput/outlinedInputClasses.js
init_extends();
function getOutlinedInputUtilityClass(slot) {
  return generateUtilityClass("MuiOutlinedInput", slot);
}
var outlinedInputClasses = _extends({}, inputBaseClasses_default, generateUtilityClasses("MuiOutlinedInput", ["root", "notchedOutline", "input"]));
var outlinedInputClasses_default = outlinedInputClasses;

export {
  getOutlinedInputUtilityClass,
  outlinedInputClasses_default
};
//# sourceMappingURL=chunk-T45RYIZ6.js.map
