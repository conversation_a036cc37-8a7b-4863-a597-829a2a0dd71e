import {
  SvgIcon_default
} from "./chunk-3H5FQ726.js";
import {
  require_jsx_runtime
} from "./chunk-D4DBS43D.js";
import {
  _extends,
  init_extends
} from "./chunk-4GAI7T4A.js";
import {
  require_react
} from "./chunk-R56R2YIZ.js";
import {
  __toESM
} from "./chunk-BYPFWIQ6.js";

// node_modules/@mui/material/utils/createSvgIcon.js
init_extends();
var React = __toESM(require_react());
var import_jsx_runtime = __toESM(require_jsx_runtime());
function createSvgIcon(path, displayName) {
  function Component(props, ref) {
    return (0, import_jsx_runtime.jsx)(SvgIcon_default, _extends({
      "data-testid": `${displayName}Icon`,
      ref
    }, props, {
      children: path
    }));
  }
  if (true) {
    Component.displayName = `${displayName}Icon`;
  }
  Component.muiName = SvgIcon_default.muiName;
  return React.memo(React.forwardRef(Component));
}

export {
  createSvgIcon
};
//# sourceMappingURL=chunk-2WMRNP5B.js.map
