{"version": 3, "sources": ["../../@mui/lab/TimelineItem/TimelineItem.js", "../../@mui/lab/TimelineItem/timelineItemClasses.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"position\", \"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { isMuiElement } from '@mui/material/utils';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { timelineContentClasses } from '../TimelineContent';\nimport { timelineOppositeContentClasses } from '../TimelineOppositeContent';\nimport TimelineContext from '../Timeline/TimelineContext';\nimport { getTimelineItemUtilityClass } from './timelineItemClasses';\nimport convertTimelinePositionToClass from '../internal/convertTimelinePositionToClass';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    position,\n    classes,\n    hasOppositeContent\n  } = ownerState;\n  const slots = {\n    root: ['root', convertTimelinePositionToClass(position), !hasOppositeContent && 'missingOppositeContent']\n  };\n  return composeClasses(slots, getTimelineItemUtilityClass, classes);\n};\nconst TimelineItemRoot = styled('li', {\n  name: 'MuiTimelineItem',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[convertTimelinePositionToClass(ownerState.position)]];\n  }\n})(({\n  ownerState\n}) => _extends({\n  listStyle: 'none',\n  display: 'flex',\n  position: 'relative',\n  minHeight: 70\n}, ownerState.position === 'left' && {\n  flexDirection: 'row-reverse'\n}, (ownerState.position === 'alternate' || ownerState.position === 'alternate-reverse') && {\n  [`&:nth-of-type(${ownerState.position === 'alternate' ? 'even' : 'odd'})`]: {\n    flexDirection: 'row-reverse',\n    [`& .${timelineContentClasses.root}`]: {\n      textAlign: 'right'\n    },\n    [`& .${timelineOppositeContentClasses.root}`]: {\n      textAlign: 'left'\n    }\n  }\n}, !ownerState.hasOppositeContent && {\n  '&::before': {\n    content: '\"\"',\n    flex: 1,\n    padding: '6px 16px'\n  }\n}));\nconst TimelineItem = /*#__PURE__*/React.forwardRef(function TimelineItem(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimelineItem'\n  });\n  const {\n      position: positionProp,\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    position: positionContext\n  } = React.useContext(TimelineContext);\n  let hasOppositeContent = false;\n  React.Children.forEach(props.children, child => {\n    if (isMuiElement(child, ['TimelineOppositeContent'])) {\n      hasOppositeContent = true;\n    }\n  });\n  const ownerState = _extends({}, props, {\n    position: positionProp || positionContext || 'right',\n    hasOppositeContent\n  });\n  const classes = useUtilityClasses(ownerState);\n  const contextValue = React.useMemo(() => ({\n    position: ownerState.position\n  }), [ownerState.position]);\n  return /*#__PURE__*/_jsx(TimelineContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(TimelineItemRoot, _extends({\n      className: clsx(classes.root, className),\n      ownerState: ownerState,\n      ref: ref\n    }, other))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TimelineItem.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The position where the timeline's item should appear.\n   */\n  position: PropTypes.oneOf(['alternate-reverse', 'alternate', 'left', 'right']),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TimelineItem;", "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTimelineItemUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineItem', slot);\n}\nconst timelineItemClasses = generateUtilityClasses('MuiTimelineItem', ['root', 'positionLeft', 'positionRight', 'positionAlternate', 'positionAlternateReverse', 'missingOppositeContent']);\nexport default timelineItemClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AAEA,YAAuB;AACvB,wBAAsB;;;ACJf,SAAS,4BAA4B,MAAM;AAChD,SAAO,qBAAqB,mBAAmB,IAAI;AACrD;AACA,IAAM,sBAAsB,uBAAuB,mBAAmB,CAAC,QAAQ,gBAAgB,iBAAiB,qBAAqB,4BAA4B,wBAAwB,CAAC;AAC1L,IAAO,8BAAQ;;;ADUf,yBAA4B;AAZ5B,IAAM,YAAY,CAAC,YAAY,WAAW;AAa1C,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,+BAA+B,QAAQ,GAAG,CAAC,sBAAsB,wBAAwB;AAAA,EAC1G;AACA,SAAO,eAAe,OAAO,6BAA6B,OAAO;AACnE;AACA,IAAM,mBAAmB,eAAO,MAAM;AAAA,EACpC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,OAAO,+BAA+B,WAAW,QAAQ,CAAC,CAAC;AAAA,EAClF;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AACF,MAAM,SAAS;AAAA,EACb,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AACb,GAAG,WAAW,aAAa,UAAU;AAAA,EACnC,eAAe;AACjB,IAAI,WAAW,aAAa,eAAe,WAAW,aAAa,wBAAwB;AAAA,EACzF,CAAC,iBAAiB,WAAW,aAAa,cAAc,SAAS,KAAK,GAAG,GAAG;AAAA,IAC1E,eAAe;AAAA,IACf,CAAC,MAAM,+BAAuB,IAAI,EAAE,GAAG;AAAA,MACrC,WAAW;AAAA,IACb;AAAA,IACA,CAAC,MAAM,uCAA+B,IAAI,EAAE,GAAG;AAAA,MAC7C,WAAW;AAAA,IACb;AAAA,EACF;AACF,GAAG,CAAC,WAAW,sBAAsB;AAAA,EACnC,aAAa;AAAA,IACX,SAAS;AAAA,IACT,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF,CAAC,CAAC;AACF,IAAM,eAAkC,iBAAW,SAASA,cAAa,SAAS,KAAK;AACrF,QAAM,QAAQ,cAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF,UAAU;AAAA,IACV;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM;AAAA,IACJ,UAAU;AAAA,EACZ,IAAU,iBAAW,uBAAe;AACpC,MAAI,qBAAqB;AACzB,EAAM,eAAS,QAAQ,MAAM,UAAU,WAAS;AAC9C,QAAI,qBAAa,OAAO,CAAC,yBAAyB,CAAC,GAAG;AACpD,2BAAqB;AAAA,IACvB;AAAA,EACF,CAAC;AACD,QAAM,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,IACrC,UAAU,gBAAgB,mBAAmB;AAAA,IAC7C;AAAA,EACF,CAAC;AACD,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,eAAqB,cAAQ,OAAO;AAAA,IACxC,UAAU,WAAW;AAAA,EACvB,IAAI,CAAC,WAAW,QAAQ,CAAC;AACzB,aAAoB,mBAAAC,KAAK,wBAAgB,UAAU;AAAA,IACjD,OAAO;AAAA,IACP,cAAuB,mBAAAA,KAAK,kBAAkB,SAAS;AAAA,MACrD,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,MACvC;AAAA,MACA;AAAA,IACF,GAAG,KAAK,CAAC;AAAA,EACX,CAAC;AACH,CAAC;AACD,OAAwC,aAAa,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQtF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,UAAU,kBAAAA,QAAU,MAAM,CAAC,qBAAqB,aAAa,QAAQ,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA,EAI7E,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,uBAAQ;", "names": ["TimelineItem", "_jsx", "PropTypes"]}